{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:ci": "jest --ci --coverage --watchAll=false", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "cy:run": "cypress run --browser chrome --headless", "cy:open": "cypress open", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@auth0/nextjs-auth0": "^3.5.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "axios": "^1.10.0", "next": "15.4.1", "playwright": "^1.54.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@storybook/addon-docs": "^9.0.17", "@storybook/addon-onboarding": "^9.0.17", "@storybook/nextjs": "^9.0.17", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cypress": "^13.11.0", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-plugin-storybook": "^9.0.17", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "storybook": "^9.0.17", "tailwindcss": "^3.4.17", "autoprefixer": "^10.4.20", "typescript": "^5"}}