import React, { useState } from 'react';
import Link from 'next/link';
import { Bill } from '../../types';
import { buildBillSlug } from '../../utils/slug';
import { StatusBadge } from './StatusBadge';
import { ActionCounter } from './ActionCounter';
import { QuickStat } from './QuickStat';
import { GroupPill } from './GroupPill';
import { ValuesAnalysisTags } from './ValuesAnalysisTags';
import { BillSponsors } from './BillSponsors';
import { SocialShare } from '../shared/SocialShare';
import { formatCompactNumber, formatTrend } from '../../utils/formatNumber';

interface EnhancedBillCardProps {
  bill: Bill;
  onTakeAction: (bill: Bill) => void;
  onViewDetails?: (bill: Bill) => void;
  usePageNavigation?: boolean; // If true, use the new full page instead of modal
}

// Simple icons as SVG components
const ClockIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const ChevronDownIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const ChevronUpIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
  </svg>
);

const MegaphoneIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
  </svg>
);

const DollarSignIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
);

const UsersIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
  </svg>
);

// Helper functions for extracting key information
const extractTimeline = (timeline: any) => {
  if (!timeline) return 'TBD';

  const content = timeline.content?.toLowerCase() || '';
  if (content.includes('immediate')) return 'Immediate';
  if (content.includes('days')) {
    const match = content.match(/(\d+)\s*days?/);
    return match ? `${match[1]} days` : 'Soon';
  }
  if (content.includes('month')) return 'This month';
  if (content.includes('week')) return 'This week';
  return 'TBD';
};

const extractCostLevel = (costImpact: any) => {
  if (!costImpact) return 'TBD';

  const content = costImpact.content?.toLowerCase() || '';
  if (content.includes('billion')) return 'High';
  if (content.includes('million')) return 'Medium';
  if (content.includes('minimal') || content.includes('low')) return 'Low';
  if (content.includes('no cost') || content.includes('revenue')) return 'Neutral';
  return 'TBD';
};

export const EnhancedBillCard: React.FC<EnhancedBillCardProps> = ({ bill, onTakeAction, usePageNavigation = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Extract key metrics from bill data using helper functions
  const getTimelineStatus = () => extractTimeline(bill.summary_timeline);
  const getCostLevel = () => extractCostLevel(bill.summary_cost_impact);
  const getAffectedGroupsCount = () => bill.summary_who_affects?.affected_groups?.length || 0;

  // Mock urgency and action count (these would come from backend in real implementation)
  const urgency = bill.status === 'floor' ? 'high' : 'medium';
  const actionCount = Math.floor(Math.random() * 1000) + 50; // Mock data

  // Get the summary text and check if it needs expansion
  const getSummaryText = () => {
    return bill.tldr || bill.simple_summary || bill.summary_what_does?.content || bill.ai_summary || '';
  };

  const summaryText = getSummaryText();
  const shouldShowExpand = summaryText.length > 200;

  const handleReadMoreToggle = () => {
    setIsExpanded(!isExpanded);
    // Track analytics for read more interactions
    if (typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.track('Bill Summary Expanded', {
        billId: bill.id,
        billNumber: bill.bill_number,
        action: isExpanded ? 'collapsed' : 'expanded',
        summaryLength: summaryText.length
      });
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 overflow-hidden break-inside-avoid mb-6" data-testid="bill-card">
      {/* Header Section - Card v2 */}
      <div className="p-6 pb-2">
        {/* Top Row: Bill ID + Status + Urgency + Momentum */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className="bg-blue-100 text-blue-800 h-8 px-3 rounded-full text-xs font-semibold flex items-center">
              {bill.bill_number}
            </span>
            <StatusBadge status={bill.status} urgency={urgency} />
          </div>
          {/* Momentum metric - aligned to status chip baseline */}
          <div className="flex items-center h-8">
            <div
              className="text-xs text-gray-600 flex items-center gap-1 cursor-help"
              title={`Unique actions on ModernAction in the last 7 days. ${formatCompactNumber(actionCount)} people acted on this bill, up 12% from last week.`}
            >
              <span className="font-medium text-gray-900">{formatCompactNumber(actionCount)}</span>
              <span>people acted</span>
              <span className="text-green-600 font-medium">· ↑12%</span>
            </div>
          </div>
        </div>

        {/* Title - Clamp to 2 lines with tooltip on hover */}
        <h3
          className="text-lg font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors leading-6 line-clamp-2 cursor-pointer"
          title={bill.title}
        >
          {bill.title}
        </h3>

        {/* Key Metrics Row - Only show when there are metrics to display */}
        {(getTimelineStatus() !== 'TBD' || getCostLevel() !== 'TBD' || getAffectedGroupsCount() > 0) && (
          <div className="flex items-center gap-3 mb-2 text-xs text-gray-600">
            {getTimelineStatus() !== 'TBD' && (
              <QuickStat
                icon={<ClockIcon />}
                label="Timeline"
                value={getTimelineStatus()}
              />
            )}
            {getCostLevel() !== 'TBD' && (
              <QuickStat
                icon={<DollarSignIcon />}
                label="Cost"
                value={getCostLevel()}
              />
            )}
            {getAffectedGroupsCount() > 0 && (
              <QuickStat
                icon={<UsersIcon />}
                label="Affected"
                value={`${getAffectedGroupsCount()} groups`}
              />
            )}
          </div>
        )}
      </div>

      {/* Content Section - Tight spacing for natural flow */}
      <div className="px-6 pt-2 pb-3">
        {/* Summary - Animated expand/collapse */}
        <div className="mb-4">
          {bill.tldr && (
            <div className="flex items-center gap-2 mb-2">
              <span className="bg-blue-100 text-blue-800 h-6 px-2 rounded-full text-xs font-semibold flex items-center">Summary</span>
            </div>
          )}
          <div className="relative">
            {summaryText ? (
              <div
                className={`${shouldShowExpand ? 'cursor-pointer' : ''}`}
                onClick={shouldShowExpand ? handleReadMoreToggle : undefined}
                onKeyDown={shouldShowExpand ? (e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleReadMoreToggle();
                  }
                } : undefined}
                tabIndex={shouldShowExpand ? 0 : -1}
                role={shouldShowExpand ? 'button' : undefined}
                aria-expanded={shouldShowExpand ? isExpanded : undefined}
                aria-label={shouldShowExpand ? (isExpanded ? 'Show less summary' : 'Show more summary') : undefined}
              >
                <p
                  className={`text-gray-800 text-sm leading-relaxed transition-all duration-150 ease-out ${
                    isExpanded ? '' : 'line-clamp-3'
                  }`}
                >
                  {summaryText}
                </p>
                {shouldShowExpand && (
                  <div className="text-blue-600 text-sm font-medium hover:text-blue-700 mt-2 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-2 rounded px-1">
                    {isExpanded ? (
                      <span className="inline-flex items-center gap-1">
                        Show less
                        <span className="transform transition-transform duration-150 rotate-180">
                          <ChevronDownIcon />
                        </span>
                      </span>
                    ) : (
                      <span className="inline-flex items-center gap-1">
                        Read more
                        <span className="transform transition-transform duration-150">
                          <ChevronDownIcon />
                        </span>
                      </span>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500 text-sm leading-relaxed">
                <p className="mb-2">No summary yet — <a href={`/bills/${bill.id}`} className="text-blue-600 hover:text-blue-700 underline">read the bill text</a></p>
                <button className="text-blue-600 text-sm font-medium hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-2 rounded px-1">
                  Generate summary
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Affected Groups Tags */}
        {bill.summary_who_affects?.affected_groups && bill.summary_who_affects.affected_groups.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {bill.summary_who_affects.affected_groups.slice(0, 2).map((group, index) => (
                <GroupPill key={index} group={group} />
              ))}
              {bill.summary_who_affects.affected_groups.length > 2 && (
                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  +{bill.summary_who_affects.affected_groups.length - 2}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Values Analysis Tags */}
        {bill.values_tags && bill.values_tags.length > 0 && (
          <div className="mb-4">
            <ValuesAnalysisTags
              tags={bill.values_tags}
              variant="compact"
              maxTags={3}
            />
          </div>
        )}

        {/* Sponsor Information */}
        {(bill.sponsor_name || (bill.cosponsors && bill.cosponsors.length > 0)) && (
          <div className="mb-5">
            <BillSponsors
              sponsorName={bill.sponsor_name}
              sponsorParty={bill.sponsor_party}
              sponsorState={bill.sponsor_state}
              sponsorBioguideId={bill.sponsor_bioguide_id}
              cosponsors={bill.cosponsors}
              maxCosponsorsShown={2}
              className="border-0 p-0 bg-transparent"
            />
          </div>
        )}

      </div>

      {/* Action Section - Consistent bottom spacing */}
      <div className="px-6 pt-2 pb-6 border-t border-gray-100">
        <div className="space-y-3">
          {usePageNavigation ? (
            <div>
              {/* Link to full page - Primary CTA */}
              <Link
                href={`/bills/${bill.id}/action`}
                className="w-full bg-blue-600 hover:bg-blue-700 active:translate-y-px text-white font-semibold py-3 px-4 rounded-lg transition-all duration-150 flex items-center justify-center gap-2 no-underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <MegaphoneIcon />
                Take Action
              </Link>

              {/* Learn More link to details page */}
              <Link
                href={`/bills/${buildBillSlug(bill.bill_number || '', bill.session_year)}`}
                className="block text-center text-blue-600 hover:text-blue-700 text-sm underline mt-2"
              >
                Learn More
              </Link>
            </div>
          ) : (
            /* Original modal button - Primary CTA */
            <button
              onClick={() => onTakeAction(bill)}
              className="w-full bg-blue-600 hover:bg-blue-700 active:translate-y-px text-white font-semibold py-3 px-4 rounded-lg transition-all duration-150 flex items-center justify-center gap-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <MegaphoneIcon />
              Take Action
            </button>
          )}

          {/* Share Button - Secondary CTA */}
          <div className="flex justify-center">
            <SocialShare
              bill={bill}
              className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
            />
          </div>
        </div>
      </div>


    </div>
  );
};


