import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { billApi } from '../services/apiClient'
import { Bill, BillStatus } from '../types'

export const metadata: Metadata = {
  title: 'ModernAction - Every Bill. Every Vote. Your Voice.',
  description: 'We track every piece of legislation in Congress, break it down in plain English, and help you tell your representatives exactly where you stand.',
}

// Server-side data fetching for active bills
async function getActiveBills(): Promise<Bill[]> {
  try {
    // Get bills that are featured first
    let bills = await billApi.searchBills({ 
      is_featured: true,
      limit: 6
    });
    
    console.log(`Found ${bills.length} featured bills`);
    
    // If we have fewer than 6 featured bills, supplement with committee bills
    if (bills.length < 6) {
      const additionalBills = await billApi.searchBills({ 
        status: BillStatus.COMMITTEE,
        limit: 12 // Get more to filter duplicates
      });
      console.log(`Found ${additionalBills.length} additional committee bills`);
      
      // Filter out duplicates by ID
      const existingIds = new Set(bills.map(b => b.id));
      const uniqueAdditionalBills = additionalBills.filter(bill => !existingIds.has(bill.id));
      
      bills = [...bills, ...uniqueAdditionalBills.slice(0, 6 - bills.length)];
    }
    
    // If still need more bills, get any recent bills
    if (bills.length < 6) {
      const moreBills = await billApi.getBills({ limit: 12 }); // Get more to filter duplicates
      console.log(`Found ${moreBills.length} more recent bills`);
      
      // Filter out duplicates by ID
      const existingIds = new Set(bills.map(b => b.id));
      const uniqueMoreBills = moreBills.filter(bill => !existingIds.has(bill.id));
      
      bills = [...bills, ...uniqueMoreBills.slice(0, 6 - bills.length)];
    }
    
    // Final deduplication and limit to 6
    const uniqueBills = bills.filter((bill, index, self) => 
      index === self.findIndex(b => b.id === bill.id)
    );
    
    return uniqueBills.slice(0, 6);
  } catch (error) {
    console.error('Error fetching active bills:', error);
    // Fallback: try to get any bills
    try {
      const fallbackBills = await billApi.getBills({ limit: 6 });
      console.log(`Fallback: found ${fallbackBills.length} bills`);
      return fallbackBills;
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
      return [];
    }
  }
}

export default async function Home() {
  const activeBills = await getActiveBills();

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="absolute inset-0">
          {/* Legislative Flow Animation Background */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-32 h-2 bg-blue-200 rounded-full animate-pulse opacity-30"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-2 bg-amber-200 rounded-full animate-pulse opacity-30 animation-delay-1000"></div>
            <div className="absolute top-3/4 left-1/2 w-28 h-2 bg-green-200 rounded-full animate-pulse opacity-30 animation-delay-2000"></div>
          </div>
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-400/10 to-blue-600/10 rounded-full blur-3xl transform translate-x-32 -translate-y-32"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-blue-400/10 to-blue-600/10 rounded-full blur-3xl transform -translate-x-32 translate-y-32"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-40">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-extrabold text-gray-900 mb-8 tracking-tight leading-tight">
              Every Bill. Every Vote.
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent highlight"> Your Voice.</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-700 mb-12 max-w-3xl mx-auto leading-relaxed font-medium hero-subtitle">
              We track every bill in Congress, break it down in plain English, and help you tell your representatives exactly where you stand.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
              <Link
                href="/bills"
                className="bg-blue-700 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-800 transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center gap-2"
              >
                View All 47 Bills in Congress →
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
              <Link
                href="#how-it-works"
                className="border-2 border-blue-700 text-blue-700 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-50 transition-all duration-200 flex items-center justify-center gap-2"
              >
                How We Track Impact
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Bar */}
      <section className="bg-white border-y border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center gap-8 mb-6 text-sm font-medium text-green-700">
              <span className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Non-partisan bill tracking
              </span>
              <span className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                AI-powered summaries
              </span>
              <span className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Direct representative contact
              </span>
            </div>
            <p className="text-gray-500 text-sm">Data sources: Congress.gov | GovTrack.us | Official Congressional Records | Updated every 6 hours</p>
          </div>
        </div>
      </section>

      {/* Active Bills Section */}
      <section id="active-bills" className="bg-gray-50 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">Bills Moving Through Congress</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto font-medium">
              Track the most urgent legislation and make your voice heard on the bills that matter
            </p>
          </div>

          {activeBills.length > 0 ? (
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              {activeBills.map((bill) => {
                const getStatusColor = (status: string) => {
                  switch (status) {
                    case 'committee': return 'from-amber-500 to-orange-500';
                    case 'floor': return 'from-red-500 to-red-600';
                    case 'passed': return 'from-green-500 to-green-600';
                    default: return 'from-blue-500 to-blue-600';
                  }
                };

                const getStatusText = (status: string) => {
                  switch (status) {
                    case 'committee': return '⏳ In Committee';
                    case 'floor': return '🗳️ Floor Vote Soon';
                    case 'passed': return '✅ Passed Chamber';
                    default: return '🔄 Active';
                  }
                };
                
                const getStatusDetail = (status: string) => {
                  switch (status) {
                    case 'committee': return 'House Judiciary - Hearing scheduled';
                    case 'floor': return 'Scheduled for House floor vote';
                    case 'passed': return 'Awaiting Senate consideration';
                    default: return 'Recently introduced';
                  }
                };

                return (
                  <div key={bill.id} className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bill-card">
                    {/* Status Tag */}
                    <div className="status-indicator">
                      <div className={`bg-gradient-to-r ${getStatusColor(bill.status)} text-white text-sm font-bold px-4 py-3 text-center bill-status-badge`}>
                        {getStatusText(bill.status)}
                      </div>
                      <div className="bg-white px-4 py-2 border-b border-gray-200">
                        <span className="text-xs font-medium text-gray-600 status-detail">{getStatusDetail(bill.status)}</span>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      {/* Bill Number & Title */}
                      <div className="mb-4">
                        <span className="text-sm font-mono font-semibold text-blue-700 mb-2 block bg-blue-50 px-2 py-1 rounded inline-block">{bill.bill_number}</span>
                        <h3 className="text-xl font-bold text-gray-900 leading-tight mb-2">
                          {bill.title.length > 80 ? `${bill.title.substring(0, 80)}...` : bill.title}
                        </h3>
                        <p className="text-sm text-gray-600 font-medium">Popular name: {bill.popular_title || bill.short_title || "Processing..."}</p>
                      </div>
                      
                      {/* Issue Tags */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                          bill.status === 'committee' ? 'bg-amber-100 text-amber-800' :
                          bill.status === 'floor' ? 'bg-red-100 text-red-800' :
                          bill.status === 'passed' ? 'bg-green-100 text-green-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {bill.chamber === 'house' ? 'House' : 'Senate'} • {bill.session_year}
                        </span>
                        {bill.sponsor_party && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                            {bill.sponsor_party}
                          </span>
                        )}
                      </div>

                      {/* AI Summary */}
                      <div className="mb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-xs font-semibold text-purple-700 bg-purple-100 px-2 py-1 rounded">🤖 AI Summary</span>
                        </div>
                        <p className="text-gray-700 text-sm leading-relaxed line-clamp-3">
                          {bill.tldr || bill.ai_summary || bill.summary || 'Legislative summary being processed...'}
                        </p>
                      </div>

                      {/* Bill Progress Indicator */}
                      <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-100">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <span className="text-sm font-semibold text-gray-800">Bill Progress</span>
                          </div>
                          <div className="text-xs font-medium text-gray-600 bg-white px-2 py-1 rounded">
                            {bill.session_year} Session
                          </div>
                        </div>
                        <div className="flex justify-between text-xs font-medium text-gray-700 mb-3">
                          <span className={bill.status === 'introduced' ? 'font-bold text-blue-700' : 'text-gray-500'}>Introduced</span>
                          <span className={bill.status === 'committee' ? 'font-bold text-amber-700' : 'text-gray-500'}>Committee</span>
                          <span className={bill.status === 'floor' ? 'font-bold text-red-700' : 'text-gray-500'}>Floor</span>
                          <span className={bill.status === 'passed' ? 'font-bold text-green-700' : 'text-gray-500'}>President</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                          <div className={`h-3 rounded-full transition-all duration-500 ${
                            bill.status === 'introduced' ? 'w-1/4 bg-gradient-to-r from-blue-500 to-blue-600' :
                            bill.status === 'committee' ? 'w-2/4 bg-gradient-to-r from-amber-500 to-orange-500' :
                            bill.status === 'floor' ? 'w-3/4 bg-gradient-to-r from-red-500 to-red-600' :
                            'w-full bg-gradient-to-r from-green-500 to-green-600'
                          }`}></div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3">
                        <Link
                          href={`/bills/${bill.id}/action`}
                          className="flex-1 bg-blue-700 text-white px-4 py-3 rounded-xl font-semibold hover:bg-blue-800 transition-all duration-200 text-center transform hover:scale-105 shadow-sm hover:shadow-md"
                        >
                          Send Message to Congress
                        </Link>
                        <Link
                          href={`/bills/${bill.id}`}
                          className="px-4 py-3 border-2 border-gray-300 text-gray-700 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
                        >
                          Read Full Analysis
                        </Link>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden bill-skeleton animate-pulse">
                  <div className="bg-gray-300 h-12"></div>
                  <div className="p-6 space-y-4">
                    <div className="h-4 bg-gray-300 rounded skeleton-line title"></div>
                    <div className="h-6 bg-gray-300 rounded skeleton-line"></div>
                    <div className="h-3 bg-gray-200 rounded skeleton-line description"></div>
                    <div className="h-3 bg-gray-200 rounded skeleton-line description"></div>
                    <div className="h-16 bg-gray-100 rounded skeleton-line progress"></div>
                    <div className="flex gap-3">
                      <div className="flex-1 h-12 bg-gray-300 rounded-xl"></div>
                      <div className="w-20 h-12 bg-gray-200 rounded-xl"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="text-center">
            <Link
              href="/bills"
              className="inline-flex items-center gap-2 bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-800 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              View All 147 Bills Active This Week
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="bg-white py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">How It Works</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto font-medium">
              From complex legislation to personalized action in 5 simple steps
            </p>
          </div>

          {/* Desktop: Horizontal timeline, Mobile: Vertical cards */}
          <div className="hidden md:block">
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute top-16 left-0 right-0 h-1 bg-gradient-to-r from-blue-200 via-amber-200 via-green-200 via-purple-200 to-red-200 rounded-full"></div>
              
              <div className="grid grid-cols-5 gap-8">
                {/* Step 1: UNDERSTAND */}
                <div className="text-center relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mx-auto mb-6 text-white shadow-lg relative z-10">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">UNDERSTAND</h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    We break down complex bills into clear summaries you can actually understand
                  </p>
                </div>

                {/* Step 2: DECIDE */}
                <div className="text-center relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-amber-600 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white shadow-lg relative z-10">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l3-3 3 3m-3-3v8m0-13a9 9 0 110 18 9 9 0 010-18z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">DECIDE</h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    Choose your stance and tell us why - select from common reasons or add your own
                  </p>
                </div>

                {/* Step 3: PERSONALIZE */}
                <div className="text-center relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white shadow-lg relative z-10">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V5a1 1 0 011-1h3a1 1 0 001-1V2a2 2 0 012-2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">PERSONALIZE</h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    Our AI crafts a unique letter based on your reasons and perspective
                  </p>
                </div>

                {/* Step 4: DELIVER */}
                <div className="text-center relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white shadow-lg relative z-10">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">DELIVER</h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    We send your message directly to your representatives via email
                  </p>
                </div>

                {/* Step 5: TRACK */}
                <div className="text-center relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-red-600 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white shadow-lg relative z-10">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">TRACK</h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    Follow the bill&rsquo;s progress and see how your representative voted
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile: Vertical timeline */}
          <div className="md:hidden space-y-8">
            {[
              {
                title: 'UNDERSTAND',
                description: 'We break down complex bills into clear summaries you can actually understand',
                icon: 'document',
                color: 'from-blue-600 to-blue-700'
              },
              {
                title: 'DECIDE', 
                description: 'Choose your stance and tell us why - select from common reasons or add your own',
                icon: 'thumb',
                color: 'from-amber-600 to-orange-600'
              },
              {
                title: 'PERSONALIZE',
                description: 'Our AI crafts a unique letter based on your reasons and perspective', 
                icon: 'magic',
                color: 'from-green-600 to-emerald-600'
              },
              {
                title: 'DELIVER',
                description: 'We send your message directly to your representatives via email',
                icon: 'mail',
                color: 'from-purple-600 to-indigo-600'
              },
              {
                title: 'TRACK',
                description: 'Follow the bill&rsquo;s progress and see how your representative voted',
                icon: 'chart', 
                color: 'from-red-600 to-pink-600'
              }
            ].map((step, index) => (
              <div key={step.title} className="flex gap-4 items-start">
                <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center text-white shadow-lg flex-shrink-0`}>
                  <span className="font-bold text-sm">{index + 1}</span>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{step.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why ModernAction is Different */}
      <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">Complete Civic Intelligence</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto font-medium">
              ModernAction is the Bloomberg Terminal for civic engagement
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">📊</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Every Single Bill</h3>
              <p className="text-gray-600 leading-relaxed">
                We track 100% of federal legislation, not just hot-button issues. Nothing slips through.
              </p>
            </div>
            
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">📋</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Your Civic Memory</h3>
              <p className="text-gray-600 leading-relaxed">
                See every stance you&rsquo;ve taken, every message sent, and how your reps responded.
              </p>
            </div>
            
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">📈</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Representative Report Card</h3>
              <p className="text-gray-600 leading-relaxed">
                Track how often your representatives vote in line with your stated positions.
              </p>
            </div>
            
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🔔</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Bill Lifecycle Tracking</h3>
              <p className="text-gray-600 leading-relaxed">
                Follow bills from introduction to final vote. Get notified at every critical stage.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Proof Section */}
      <section className="bg-white py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">Real Impact, Real Results</h2>
              <div className="text-6xl font-bold text-blue-600 mb-4">73%</div>
              <p className="text-xl text-gray-700 mb-8 font-medium">
                of campaigns influenced outcomes when citizens took action together
              </p>
              <Link
                href="/about#impact"
                className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium"
              >
                See Our Impact Stories
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>

            <div className="space-y-6">
              {/* Case Study Examples */}
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                <h4 className="font-bold text-gray-900 mb-2">Climate Action Bill</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Passed with bipartisan support after 24,000 citizens took action
                </p>
                <div className="text-green-600 font-medium text-sm">✓ Successful</div>
              </div>
              
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                <h4 className="font-bold text-gray-900 mb-2">Healthcare Reform Act</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Key amendments added after 18,000 messages to representatives
                </p>
                <div className="text-blue-600 font-medium text-sm">→ Modified</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values-Based Onboarding Prompt */}
      <section className="bg-gray-50 py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8 leading-tight">What matters most to you?</h2>
          
          <div className="bg-blue-50 rounded-2xl p-6 mb-10 text-left max-w-2xl mx-auto">
            <h3 className="font-semibold text-gray-900 mb-3">Select issues you care about to:</h3>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start gap-3">
                <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Get notified when relevant bills are introduced
              </li>
              <li className="flex items-start gap-3">
                <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                See your personalized bill feed first
              </li>
              <li className="flex items-start gap-3">
                <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Track your representative's votes on these topics
              </li>
            </ul>
          </div>
          
          <div className="flex flex-wrap justify-center gap-3 mb-10">
            {['Healthcare', 'Environment', 'Education', 'Economy', 'Civil Rights', 'Immigration', 'Technology', 'Defense'].map((issue) => (
              <button
                key={issue}
                className="px-6 py-3 bg-gray-100 hover:bg-blue-100 hover:text-blue-800 text-gray-700 rounded-full transition-colors font-medium"
              >
                {issue}
              </button>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/bills"
              className="bg-blue-700 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-800 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              Start Tracking Bills
            </Link>
            <Link
              href="/bills"
              className="text-gray-700 hover:text-gray-900 px-8 py-4 rounded-xl text-lg font-medium transition-colors border-2 border-gray-300 hover:border-gray-400"
            >
              Browse All 8,234 Bills
            </Link>
          </div>
        </div>
      </section>
      
      {/* Final CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">Ready to make your voice count?</h2>
          <p className="text-xl mb-8 text-blue-100 font-medium">
            Join thousands of citizens holding Congress accountable
          </p>
          <div className="mb-8">
            <Link
              href="/bills"
              className="bg-white text-blue-700 px-10 py-4 rounded-xl text-xl font-bold hover:bg-gray-50 transition-all duration-200 transform hover:scale-105 shadow-xl inline-block"
            >
              Start Tracking Bills
            </Link>
          </div>
          <div className="text-sm text-blue-200 font-medium">
            Free forever • No spam • Cancel anytime
          </div>
        </div>
      </section>
    </div>
  );
}