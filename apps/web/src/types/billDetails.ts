// Types for Bill Details API (BillDetailsResponse)

export interface Citation {
  quote: string;
  start_offset: number;
  end_offset: number;
  heading?: string | null;
  anchor_id?: string | null;
}

export interface SectionWithCitations {
  content: string;
  citations: Citation[];
}

export interface ProvisionItem {
  content: string;
  citations: Citation[];
}

export interface TimelineItem {
  content: string;
  citations: Citation[];
}

// New comprehensive analysis types
export interface PrimaryMechanism {
  mechanism: string;
  affected_parties: string;
  requirements: string;
  enforcement?: string | null;
  timeline?: string | null;
  citations: Citation[];
  importance_score: number;
}

export interface SecondaryProvision {
  provision: string;
  affected_parties: string;
  citations: Citation[];
}

export interface EnforcementFramework {
  mechanisms: string[];
  penalties: string[];
  enforcing_agencies: string[];
  summary: string;
}

export interface FundingImpacts {
  changes: Array<{
    type: string;
    entity: string;
    amount: string;
    conditions: string;
  }>;
  affected_entities: string[];
  summary: string;
}

export interface ImplementationTimelineItem {
  deadline: string;
  action: string;
  responsible_party: string;
  citations: Citation[];
}

export interface CompleteAnalysisSection {
  title: string;
  importance: string;
  summary: string;
  affected_parties: string[];
  key_actions: string[];
  citations: Citation[];
}

export interface AdditionalDetailsProvision {
  provision: string;
  type: string;
  affected_parties: string;
  details: string;
  citations: Citation[];
}

export interface AdditionalDetailsSection {
  section_title: string;
  section_type: string;
  importance: string;
  provisions: AdditionalDetailsProvision[];
}

export interface Overview {
  what_does?: SectionWithCitations | null;
  who_affects?: SectionWithCitations | null;
  why_matters?: SectionWithCitations | null;
  key_provisions?: ProvisionItem[] | null;
  cost_impact?: SectionWithCitations | null;
  timeline?: TimelineItem[] | null;

  // New comprehensive analysis fields
  primary_mechanisms?: PrimaryMechanism[] | null;
  secondary_provisions?: SecondaryProvision[] | null;
  enforcement_framework?: EnforcementFramework | null;
  funding_impacts?: FundingImpacts | null;
  implementation_timeline?: ImplementationTimelineItem[] | null;
  complete_analysis?: CompleteAnalysisSection[] | null;
  additional_details?: AdditionalDetailsSection[] | null;
}

export interface PositionReason {
  claim: string;
  justification: string;
  citations: Citation[];
}

export interface Positions {
  support_reasons?: PositionReason[] | null;
  oppose_reasons?: PositionReason[] | null;
  amend_reasons?: PositionReason[] | null;
}

export interface SourceIndexItem {
  heading?: string | null;
  start_offset: number;
  end_offset: number;
  anchor_id?: string | null;
}

export interface Metrics {
  coverage_ratio?: number | null;
  unverified_count?: number | null;
}

export interface BillDetailsResponse {
  id: string;
  bill_id: string;
  seo_slug?: string | null;
  seo_title?: string | null;
  seo_meta_description?: string | null;
  canonical_url?: string | null;
  hero_summary?: string | null;
  hero_summary_citations?: Citation[] | null;

  overview?: Overview | null;
  positions?: Positions | null;
  message_templates?: Record<string, any> | null;
  tags?: string[] | null;
  other_details?: SectionWithCitations[] | null;
  source_index?: SourceIndexItem[] | null;

  needs_human_review: boolean;
  reviewed_at?: string | null;
  reviewed_by?: string | null;
  moderation_notes?: string | null;

  metrics?: Metrics | null;

  created_at: string;
  updated_at: string;
}

