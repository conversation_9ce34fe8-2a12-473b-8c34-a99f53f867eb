"""
Quality Validation Service - HR5-118 Gold Standard Implementation
Validates bill analysis against the HR5-118 benchmark for comprehensive quality control
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class QualityLevel(Enum):
    """Quality levels for validation"""
    GOLD_STANDARD = "gold_standard"  # HR5-118 level
    HIGH_QUALITY = "high_quality"    # Meets high standards
    ACCEPTABLE = "acceptable"        # Meets minimum standards
    NEEDS_IMPROVEMENT = "needs_improvement"  # Below standards
    POOR = "poor"                    # Significantly below standards

@dataclass
class QualityMetrics:
    """Quality metrics for content validation"""
    specificity_score: float
    evidence_grounding_score: float
    comprehensiveness_score: float
    clarity_score: float
    actionability_score: float
    overall_score: float
    quality_level: QualityLevel
    issues: List[str]
    recommendations: List[str]

@dataclass
class HR5118Standard:
    """HR5-118 gold standard benchmarks"""
    # Content requirements
    min_tldr_words: int = 25
    max_tldr_words: int = 50
    min_sections: int = 5
    max_sections: int = 8
    min_affected_parties: int = 3
    min_evidence_citations: int = 2
    
    # Quality thresholds
    min_specificity_score: float = 0.8
    min_evidence_score: float = 0.85
    min_comprehensiveness_score: float = 0.75
    min_clarity_score: float = 0.8
    min_actionability_score: float = 0.7
    
    # Content patterns that indicate quality
    required_patterns = [
        r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?',  # Money amounts
        r'(?:shall|must|required|mandate)',  # Legal obligations
        r'(?:not later than|within \d+|deadline)',  # Deadlines
        r'(?:penalty|fine|enforcement)',  # Enforcement mechanisms
    ]
    
    # Generic content patterns to avoid
    generic_patterns = [
        r'comprehensive\s+(?:analysis|provisions)',
        r'multiple\s+(?:areas|aspects|provisions)',
        r'various\s+(?:stakeholders|parties|entities)',
        r'significant\s+(?:impact|implications|changes)',
        r'thorough\s+(?:review|analysis|examination)',
    ]

class QualityValidationService:
    """
    Validates bill analysis content against HR5-118 gold standards
    Provides quality scoring and improvement recommendations
    """
    
    def __init__(self):
        self.hr5118_standard = HR5118Standard()
    
    def validate_analysis_quality(self, analysis: Dict[str, Any], 
                                bill_metadata: Dict[str, Any]) -> QualityMetrics:
        """
        Comprehensive quality validation against HR5-118 standards
        
        Args:
            analysis: Complete bill analysis to validate
            bill_metadata: Bill metadata for context
            
        Returns:
            QualityMetrics with detailed scoring and recommendations
        """
        
        logger.info(f"🔍 Validating analysis quality for {bill_metadata.get('title', 'Unknown Bill')}")
        
        # Extract key content areas
        tldr = self._extract_tldr(analysis)
        complete_analysis = analysis.get('complete_analysis', [])
        affected_parties = self._extract_affected_parties(analysis)
        evidence_citations = self._extract_evidence_citations(analysis)
        
        # Calculate quality scores
        specificity_score = self._calculate_specificity_score(analysis)
        evidence_score = self._calculate_evidence_grounding_score(analysis, evidence_citations)
        comprehensiveness_score = self._calculate_comprehensiveness_score(complete_analysis)
        clarity_score = self._calculate_clarity_score(analysis)
        actionability_score = self._calculate_actionability_score(analysis)
        
        # Overall weighted score
        overall_score = self._calculate_overall_score(
            specificity_score, evidence_score, comprehensiveness_score,
            clarity_score, actionability_score
        )
        
        # Determine quality level
        quality_level = self._determine_quality_level(overall_score)
        
        # Identify issues and recommendations
        issues = self._identify_quality_issues(analysis, {
            'specificity': specificity_score,
            'evidence': evidence_score,
            'comprehensiveness': comprehensiveness_score,
            'clarity': clarity_score,
            'actionability': actionability_score
        })
        
        recommendations = self._generate_recommendations(issues, quality_level)
        
        metrics = QualityMetrics(
            specificity_score=specificity_score,
            evidence_grounding_score=evidence_score,
            comprehensiveness_score=comprehensiveness_score,
            clarity_score=clarity_score,
            actionability_score=actionability_score,
            overall_score=overall_score,
            quality_level=quality_level,
            issues=issues,
            recommendations=recommendations
        )
        
        logger.info(f"✅ Quality validation complete: {quality_level.value} ({overall_score:.2f})")
        
        return metrics
    
    def _calculate_specificity_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate how specific and detailed the content is"""
        score = 0.0
        total_checks = 0
        
        # Check for specific monetary amounts
        content_text = json.dumps(analysis).lower()
        money_patterns = len(re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', content_text))
        score += min(money_patterns * 0.2, 0.3)  # Up to 0.3 for money details
        total_checks += 0.3
        
        # Check for specific deadlines
        deadline_patterns = len(re.findall(r'(?:not later than|within \d+|deadline|days after)', content_text))
        score += min(deadline_patterns * 0.15, 0.25)  # Up to 0.25 for deadlines
        total_checks += 0.25
        
        # Check for specific legal obligations
        obligation_patterns = len(re.findall(r'(?:shall|must|required|mandate)', content_text))
        score += min(obligation_patterns * 0.1, 0.25)  # Up to 0.25 for obligations
        total_checks += 0.25
        
        # Check for specific parties/entities
        complete_analysis = analysis.get('complete_analysis', [])
        specific_parties = set()
        for section in complete_analysis:
            parties = section.get('affected_parties', [])
            specific_parties.update([p for p in parties if len(p) > 10])  # Longer names are more specific
        
        score += min(len(specific_parties) * 0.04, 0.2)  # Up to 0.2 for specific parties
        total_checks += 0.2
        
        return min(score / total_checks if total_checks > 0 else 0, 1.0)
    
    def _calculate_evidence_grounding_score(self, analysis: Dict[str, Any], 
                                          evidence_citations: List[str]) -> float:
        """Calculate how well content is grounded in evidence"""
        score = 0.0
        
        # Check evidence citation count
        citation_count = len(evidence_citations)
        if citation_count >= self.hr5118_standard.min_evidence_citations:
            score += 0.4
        else:
            score += (citation_count / self.hr5118_standard.min_evidence_citations) * 0.4
        
        # Check that sections have evidence IDs
        complete_analysis = analysis.get('complete_analysis', [])
        sections_with_evidence = sum(1 for s in complete_analysis if s.get('ev_ids'))
        if complete_analysis:
            evidence_ratio = sections_with_evidence / len(complete_analysis)
            score += evidence_ratio * 0.3
        
        # Check for evidence integration in content
        content_text = json.dumps(analysis)
        if 'ev_id' in content_text or 'evidence' in content_text.lower():
            score += 0.2
        
        # Penalize generic content
        generic_penalty = 0
        for pattern in self.hr5118_standard.generic_patterns:
            matches = len(re.findall(pattern, content_text.lower()))
            generic_penalty += matches * 0.05
        
        score = max(0, score - generic_penalty)
        score += 0.1  # Base score for having any evidence
        
        return min(score, 1.0)
    
    def _calculate_comprehensiveness_score(self, complete_analysis: List[Dict]) -> float:
        """Calculate how comprehensive the analysis is"""
        score = 0.0
        
        # Check section count
        section_count = len(complete_analysis)
        if section_count >= self.hr5118_standard.min_sections:
            score += 0.3
        else:
            score += (section_count / self.hr5118_standard.min_sections) * 0.3
        
        # Check for different importance levels
        importance_levels = set(s.get('importance', 'technical') for s in complete_analysis)
        if 'primary' in importance_levels:
            score += 0.2
        if 'secondary' in importance_levels:
            score += 0.1
        if len(importance_levels) >= 2:
            score += 0.1
        
        # Check for key content areas
        section_titles = ' '.join(s.get('title', '') for s in complete_analysis).lower()
        key_areas = ['funding', 'enforcement', 'requirement', 'penalty', 'implementation', 'reporting']
        covered_areas = sum(1 for area in key_areas if area in section_titles)
        score += (covered_areas / len(key_areas)) * 0.3
        
        return min(score, 1.0)
    
    def _calculate_clarity_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate content clarity and readability"""
        score = 0.0
        
        # Check for clear section structure
        complete_analysis = analysis.get('complete_analysis', [])
        clear_sections = sum(1 for s in complete_analysis 
                           if s.get('title') and s.get('detailed_summary') and s.get('key_actions'))
        if complete_analysis:
            structure_ratio = clear_sections / len(complete_analysis)
            score += structure_ratio * 0.4
        
        # Check for clear language (avoid jargon overload)
        content_text = json.dumps(analysis)
        word_count = len(content_text.split())
        complex_words = len(re.findall(r'\b\w{12,}\b', content_text))  # Very long words
        if word_count > 0:
            complexity_ratio = complex_words / word_count
            clarity_bonus = max(0, 0.3 - complexity_ratio * 3)  # Penalize excessive complexity
            score += clarity_bonus
        
        # Check for actionable language
        actionable_patterns = len(re.findall(r'(?:will|must|shall|requires|establishes|creates)', content_text.lower()))
        score += min(actionable_patterns * 0.02, 0.2)
        
        # Check for proper summary structure
        tldr = self._extract_tldr(analysis)
        if tldr and 20 <= len(tldr.split()) <= 60:
            score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_actionability_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate how actionable the content is for citizens"""
        score = 0.0
        
        # Check for citizen-facing positions
        positions = analysis.get('positions', {})
        support_reasons = positions.get('support_reasons', [])
        oppose_reasons = positions.get('oppose_reasons', [])
        
        if support_reasons and oppose_reasons:
            score += 0.4
        elif support_reasons or oppose_reasons:
            score += 0.2
        
        # Check for clear implications
        complete_analysis = analysis.get('complete_analysis', [])
        sections_with_parties = sum(1 for s in complete_analysis if s.get('affected_parties'))
        if complete_analysis:
            party_ratio = sections_with_parties / len(complete_analysis)
            score += party_ratio * 0.3
        
        # Check for clear actions/outcomes
        sections_with_actions = sum(1 for s in complete_analysis if s.get('key_actions'))
        if complete_analysis:
            action_ratio = sections_with_actions / len(complete_analysis)
            score += action_ratio * 0.3
        
        return min(score, 1.0)
    
    def _calculate_overall_score(self, specificity: float, evidence: float, 
                               comprehensiveness: float, clarity: float, 
                               actionability: float) -> float:
        """Calculate weighted overall quality score"""
        weights = {
            'evidence': 0.25,      # Most important - must be grounded
            'specificity': 0.25,   # Second most important - must be specific
            'comprehensiveness': 0.2,  # Must cover key areas
            'clarity': 0.15,       # Must be understandable
            'actionability': 0.15  # Must be useful for citizens
        }
        
        return (
            evidence * weights['evidence'] +
            specificity * weights['specificity'] +
            comprehensiveness * weights['comprehensiveness'] +
            clarity * weights['clarity'] +
            actionability * weights['actionability']
        )
    
    def _determine_quality_level(self, overall_score: float) -> QualityLevel:
        """Determine quality level based on overall score"""
        if overall_score >= 0.9:
            return QualityLevel.GOLD_STANDARD
        elif overall_score >= 0.8:
            return QualityLevel.HIGH_QUALITY
        elif overall_score >= 0.65:
            return QualityLevel.ACCEPTABLE
        elif overall_score >= 0.4:
            return QualityLevel.NEEDS_IMPROVEMENT
        else:
            return QualityLevel.POOR
    
    def _identify_quality_issues(self, analysis: Dict[str, Any], 
                               scores: Dict[str, float]) -> List[str]:
        """Identify specific quality issues"""
        issues = []
        
        if scores['specificity'] < 0.7:
            issues.append("Content lacks specific details (monetary amounts, deadlines, specific requirements)")
        
        if scores['evidence'] < 0.7:
            issues.append("Insufficient evidence grounding - more citations needed")
        
        if scores['comprehensiveness'] < 0.6:
            issues.append("Analysis missing key areas (funding, enforcement, implementation)")
        
        if scores['clarity'] < 0.7:
            issues.append("Content structure or language clarity needs improvement")
        
        if scores['actionability'] < 0.6:
            issues.append("Insufficient actionable information for citizens")
        
        # Check for generic content
        content_text = json.dumps(analysis).lower()
        generic_count = sum(len(re.findall(pattern, content_text)) 
                          for pattern in self.hr5118_standard.generic_patterns)
        if generic_count > 3:
            issues.append("Contains too much generic language - needs more specific content")
        
        return issues
    
    def _generate_recommendations(self, issues: List[str], 
                                quality_level: QualityLevel) -> List[str]:
        """Generate specific recommendations for improvement"""
        recommendations = []
        
        if quality_level in [QualityLevel.POOR, QualityLevel.NEEDS_IMPROVEMENT]:
            recommendations.append("Increase evidence extraction and ensure all major sections cite specific bill text")
            recommendations.append("Add specific monetary amounts, deadlines, and enforcement mechanisms")
            recommendations.append("Expand analysis to cover all major bill provisions")
        
        if "specificity" in str(issues):
            recommendations.append("Include more specific details: exact dollar amounts, precise deadlines, named entities")
        
        if "evidence" in str(issues):
            recommendations.append("Ensure every major claim references specific evidence spans with IDs")
        
        if "comprehensiveness" in str(issues):
            recommendations.append("Cover key legislative areas: funding, enforcement, implementation, penalties")
        
        if "clarity" in str(issues):
            recommendations.append("Improve content structure with clear section titles and summaries")
        
        if "actionable" in str(issues):
            recommendations.append("Add clear citizen-facing implications and position reasoning")
        
        return recommendations
    
    # Helper methods for content extraction
    def _extract_tldr(self, analysis: Dict[str, Any]) -> str:
        """Extract TL;DR from analysis"""
        # Try multiple possible locations
        tldr_locations = [
            analysis.get('tldr', ''),
            analysis.get('hero_summary', ''),
            analysis.get('summary', ''),
        ]
        
        for tldr in tldr_locations:
            if tldr and len(tldr.strip()) > 10:
                return tldr.strip()
        
        return ''
    
    def _extract_affected_parties(self, analysis: Dict[str, Any]) -> List[str]:
        """Extract all affected parties from analysis"""
        parties = set()
        
        complete_analysis = analysis.get('complete_analysis', [])
        for section in complete_analysis:
            section_parties = section.get('affected_parties', [])
            parties.update(section_parties)
        
        return list(parties)
    
    def _extract_evidence_citations(self, analysis: Dict[str, Any]) -> List[str]:
        """Extract all evidence citation IDs from analysis"""
        citations = set()
        
        # Check complete_analysis sections
        complete_analysis = analysis.get('complete_analysis', [])
        for section in complete_analysis:
            ev_ids = section.get('ev_ids', [])
            citations.update(ev_ids)
        
        # Check positions
        positions = analysis.get('positions', {})
        for position_type in ['support_reasons', 'oppose_reasons', 'amend_reasons']:
            reasons = positions.get(position_type, [])
            for reason in reasons:
                if isinstance(reason, dict):
                    ev_ids = reason.get('ev_ids', [])
                    citations.update(ev_ids)
        
        return list(citations)

# Global instance
quality_validator = QualityValidationService()

def get_quality_validator() -> QualityValidationService:
    """Get the global quality validation service instance"""
    return quality_validator