# app/services/unified_bill_processing_service.py
"""
Unified Bill Processing Service

This service combines the complete bill processing pipeline:
1. Congress.gov API data fetching
2. AI analysis with OpenAI
3. Values analysis and scoring
4. Database storage

This replaces the fragmented approach and ensures consistent processing
without campaign creation.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.services.congress_gov_api import CongressGovAPI
from app.services.ai_service import AIService
from app.services.bill_values_analysis_service import BillValuesAnalysisService
from app.services.comprehensive_bill_processing_service import ComprehensiveBillProcessingService
from app.services.bill_importance_scorer import BillImportanceScorer, ImportanceLevel
from app.services.enhanced_importance_scorer import get_enhanced_importance_scorer, EvidenceEnhancedScore
from app.models.bill import Bill, BillType, BillStatus
from app.schemas.bill import BillCreate
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class UnifiedBillProcessingService:
    """
    Unified service for complete bill processing pipeline.

    Handles the entire flow from Congress.gov API to fully processed bills
    with AI analysis and values scoring, ready for user actions.
    """

    def __init__(self, db: Session):
        self.db = db
        self.congress_api = CongressGovAPI()
        self.ai_service = AIService()
        self.values_service = BillValuesAnalysisService(db)
        self.importance_scorer = BillImportanceScorer()
        self.enhanced_importance_scorer = get_enhanced_importance_scorer()
        # DISABLED: Expensive comprehensive service that burns money
        # self.comprehensive_service = ComprehensiveBillProcessingService(db)
        from app.services.bill_details_service import BillDetailsService
        self.details_service = BillDetailsService(db)

    async def process_bill_by_number(
        self,
        bill_number: str,
        congress_session: int = 118,
        environment: str = "development",
        use_comprehensive: bool = True,
        use_enhanced_analysis: bool = True  # Default to new enriched analysis
    ) -> Dict[str, Any]:
        """
        Process a single bill by its number through the complete pipeline.

        Args:
            bill_number: Bill number like "HR5", "S1234"
            congress_session: Congress session number
            environment: Environment context (development, staging, production)

        Returns:
            Processing results with bill data and status
        """
        logger.info(f"Starting unified processing for {bill_number} in {environment}")
        logger.info(f"🔍 DEBUG: use_enhanced_analysis={use_enhanced_analysis} (type: {type(use_enhanced_analysis)})")
        logger.info(f"🔍 DEBUG: use_comprehensive={use_comprehensive} (type: {type(use_comprehensive)})")

        try:
            # Step 1: Check if bill already exists
            existing_bill = self._check_existing_bill(bill_number, congress_session)
            if existing_bill:
                logger.info(f"Bill {bill_number} already exists, updating if needed")
                return await self._update_existing_bill(existing_bill, environment)

            # Step 2: Fetch bill data from Congress.gov API
            bill_metadata = await self._fetch_bill_metadata(bill_number, congress_session)
            if not bill_metadata:
                return {
                    "success": False,
                    "error": f"Could not fetch metadata for bill {bill_number}",
                    "bill_number": bill_number
                }

            # Step 3: Get full bill text
            full_text = await self._fetch_bill_text(bill_metadata, congress_session)

            # Step 4: Create bill record first (without AI results)
            # Idempotency: if another record already exists with same congress_gov_id, load and update it
            try:
                existing_by_cgid = None
                if bill_metadata:
                    cgid = f"{bill_metadata.get('congress_session', 118)}-{bill_metadata.get('type', '').lower()}-{bill_metadata.get('number', '')}"
                    existing_by_cgid = self.db.query(Bill).filter(Bill.congress_gov_id == cgid).first()
                if existing_by_cgid:
                    bill = await self._update_existing_bill(existing_by_cgid, environment)
                    # ensure bill is a Bill instance for subsequent steps
                    bill = self.db.query(Bill).get(bill.get("bill_id")) if isinstance(bill, dict) else bill
                else:
                    # Create bill record with minimal data first
                    bill = await self._create_bill_record(bill_metadata, full_text, {})
            except Exception as e:
                logger.error(f"Error in bill creation/update: {e}")
                # Rollback the session to clear any dirty state
                self.db.rollback()
                try:
                    bill = await self._create_bill_record(bill_metadata, full_text, {})
                except Exception as e2:
                    logger.error(f"Failed to create bill record after rollback: {e2}")
                    self.db.rollback()
                    raise e2

            # Validate bill has valid ID before proceeding
            if not bill or not hasattr(bill, 'id') or bill.id is None:
                error_msg = f"Bill creation failed - no valid bill ID for {bill_number}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "bill_number": bill_number,
                    "environment": environment
                }

            # Step 4.5: Score bill importance to determine processing level
            importance_score = self.importance_scorer.score_bill(
                title=bill.title or "",
                summary=bill.summary or "",
                bill_number=bill.bill_number or ""
            )
            
            logger.info(f"📊 Bill importance: {importance_score.score}/100 ({importance_score.level.value})")
            logger.info(f"🤖 Auto-process with AI: {importance_score.auto_process}")
            logger.info(f"📝 Reason: {importance_score.reason}")
            
            # Update bill priority score based on importance
            bill.priority_score = importance_score.score
            self.db.commit()

            # Step 5: AI analysis with cost optimization (now that bill exists)
            # Check if comprehensive analysis is enabled (can be disabled for cost savings)
            comprehensive_enabled = settings.AI_ENABLE_COMPREHENSIVE_ANALYSIS

            logger.info(f"🔍 DEBUG: comprehensive_enabled={comprehensive_enabled}, use_enhanced_analysis={use_enhanced_analysis}")
            logger.info(f"🔍 DEBUG: AI_ENABLE_COMPREHENSIVE_ANALYSIS setting={settings.AI_ENABLE_COMPREHENSIVE_ANALYSIS}")

            # Only run AI analysis for important bills (to save costs)
            should_run_ai = importance_score.auto_process or use_enhanced_analysis

            # Determine which analysis path to use
            if should_run_ai and use_enhanced_analysis:
                logger.info("🎯 Using BALANCED analysis (premium user content + efficient background)")
                # Use the new balanced analysis
                cost_optimized_result = await self.ai_service.analyze_bill_balanced(
                    full_text, bill_metadata, source_index=None
                )
                if cost_optimized_result.get("success"):
                    # Convert to expected format
                    ai_results = self._convert_cost_optimized_to_legacy(cost_optimized_result)

                    # Use details_payload from balanced analysis if available
                    details_payload = cost_optimized_result.get('details_payload')
                    if not details_payload:
                        # Fallback to conversion method
                        details_payload = self._convert_cost_optimized_to_details(cost_optimized_result)

                    cost = cost_optimized_result.get('_metadata', {}).get('cost', 0)
                    logger.info(f"💰 Enhanced span-grounded analysis completed: ${cost:.4f} cost")
                else:
                    logger.error("Enhanced analysis failed - NOT falling back to expensive legacy methods")
                    return {
                        "success": False,
                        "error": "Enhanced analysis failed and expensive fallback disabled",
                        "bill_id": bill.id if 'bill' in locals() else None
                    }
            elif should_run_ai and not comprehensive_enabled:
                logger.info("🎯 Using BALANCED analysis (cost-optimized path)")
                # Use the balanced analysis for all paths
                cost_optimized_result = await self.ai_service.analyze_bill_balanced(
                    full_text, bill_metadata, source_index=None
                )
                if cost_optimized_result.get("success"):
                    # Convert to expected format
                    ai_results = self._convert_cost_optimized_to_legacy(cost_optimized_result)
                    details_payload = self._convert_cost_optimized_to_details(cost_optimized_result)
                    logger.info(f"💰 Cost-optimized analysis completed: {cost_optimized_result.get('_metadata', {}).get('cost', 'unknown')} cost")
                else:
                    logger.error("Cost-optimized analysis failed - NOT falling back to expensive legacy")
                    return {
                        "success": False,
                        "error": "Cost-optimized analysis failed and expensive fallback disabled",
                        "bill_id": bill.id if 'bill' in locals() else None
                    }
            else:
                logger.info(f"⚡ Skipping AI analysis - bill importance: {importance_score.level.value} (score: {importance_score.score})")
                logger.info(f"💡 Reason: {importance_score.reason}")
                ai_results = {}
                details_payload = {}

            # Step 6: Update bill record with AI results
            if ai_results:
                try:
                    bill.ai_summary = ai_results.get("ai_summary", "")
                    bill.tldr = ai_results.get("tldr", "")
                    bill.support_reasons = ai_results.get("support_reasons", [])
                    bill.oppose_reasons = ai_results.get("oppose_reasons", [])
                    bill.amend_reasons = ai_results.get("amend_reasons", [])
                    bill.message_templates = ai_results.get("message_templates", {})
                    bill.tags = ai_results.get("tags", [])
                    bill.ai_processed_at = datetime.utcnow()
                    self.db.commit()
                    logger.info(f"Updated bill {bill.id} with AI results")
                    
                    # Phase 3: Re-evaluate importance with evidence-driven scoring
                    enhanced_importance = await self._re_evaluate_importance_with_evidence(bill, full_text, ai_results)
                    
                except Exception as e:
                    logger.error(f"Failed to update bill with AI results: {e}")
                    self.db.rollback()

            # Step 7: Create bill_details using bill_id (not ORM object)
            if details_payload and bill:
                try:
                    # Ensure bill is committed and get bill_id
                    self.db.flush()  # Force INSERT, assign bill.id
                    bill_id = bill.id
                    if not bill_id:
                        raise ValueError(f"Bill {bill.bill_number} has no ID after flush")

                    logger.info(f"Creating bill_details for bill_id {bill_id} ({bill.bill_number})")
                    self.details_service.create_or_update_details_by_id(bill_id, full_text, details_payload)
                    logger.info(f"Successfully created bill_details for bill_id {bill_id}")
                except Exception as e:
                    logger.error(f"Failed to create bill_details for bill {getattr(bill, 'bill_number', 'unknown')}: {e}")
                    # Don't fail the entire process if bill_details creation fails
                    # The bill itself was created successfully
                    import traceback
                    logger.error(f"Full traceback: {traceback.format_exc()}")

            # Step 7: Run values analysis
            values_analysis = await self._run_values_analysis(bill)

            # Step 8: Return success result
            return {
                "success": True,
                "bill_id": bill.id,
                "bill_number": bill.bill_number,
                "title": bill.title,
                "environment": environment,
                "processing_steps": {
                    "metadata_fetched": True,
                    "full_text_fetched": bool(full_text),
                    "ai_analysis_completed": bool(ai_results),
                    "values_analysis_completed": bool(values_analysis),
                    "ready_for_users": True
                },
                "ai_summary": ai_results.get("ai_summary", "") if ai_results else "",
                "values_scores": {
                    "democracy_support": values_analysis.democracy_support_score if values_analysis else 0,
                    "human_rights_support": values_analysis.human_rights_support_score if values_analysis else 0,
                    "environmental_support": values_analysis.environmental_support_score if values_analysis else 0,
                    "needs_review": values_analysis.requires_human_review if values_analysis else False
                } if values_analysis else None,
                "message": f"Successfully processed {bill_number} through complete pipeline"
            }

        except Exception as e:
            logger.error(f"Unified processing failed for {bill_number}: {e}")
            return {
                "success": False,
                "error": str(e),
                "bill_number": bill_number,
                "environment": environment
            }

    async def process_recent_bills(
        self,
        limit: int = 5,
        congress_session: int = 118,
        environment: str = "development"
    ) -> Dict[str, Any]:
        """
        Process recent bills from Congress.gov through the complete pipeline.

        Args:
            limit: Maximum number of bills to process
            congress_session: Congress session number
            environment: Environment context

        Returns:
            Batch processing results
        """
        logger.info(f"Processing {limit} recent bills in {environment}")

        try:
            # Fetch recent bills from Congress.gov
            recent_bills = self.congress_api.get_recent_bills(
                congress=congress_session,
                limit=limit
            )

            if not recent_bills:
                return {
                    "success": False,
                    "error": "No recent bills found",
                    "environment": environment
                }

            # Process each bill
            results = {
                "success": True,
                "environment": environment,
                "total_bills": len(recent_bills),
                "processed_bills": [],
                "failed_bills": [],
                "skipped_bills": [],
                "errors": []
            }

            for bill_data in recent_bills:
                try:
                    bill_number = bill_data.get("number", "")
                    if not bill_number:
                        continue

                    # Check if already exists
                    existing = self._check_existing_bill(bill_number, congress_session)
                    if existing:
                        results["skipped_bills"].append(bill_number)
                        continue

                    # Process the bill
                    result = await self._process_bill_from_data(bill_data, environment)

                    if result["success"]:
                        results["processed_bills"].append({
                            "bill_number": bill_number,
                            "bill_id": result["bill_id"],
                            "title": result["title"]
                        })
                    else:
                        results["failed_bills"].append(bill_number)
                        results["errors"].append(f"{bill_number}: {result['error']}")

                except Exception as e:
                    logger.error(f"Error processing bill from batch: {e}")
                    results["errors"].append(f"Batch processing error: {str(e)}")

            results["message"] = f"Processed {len(results['processed_bills'])} bills successfully"
            return results

        except Exception as e:
            logger.error(f"Batch processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "environment": environment
            }

    async def _fetch_bill_metadata(self, bill_number: str, congress_session: int) -> Optional[Dict[str, Any]]:
        """Fetch bill metadata from Congress.gov API"""
        try:
            parsed = self.congress_api.parse_bill_number(bill_number)
            parsed['congress'] = congress_session

            bill_data = self.congress_api.get_bill_by_number(
                congress=parsed['congress'],
                bill_type=parsed['bill_type'],
                bill_number=parsed['number']
            )

            if bill_data:
                return {
                    **bill_data,
                    'parsed_number': parsed,
                    'congress_session': congress_session
                }
            return None

        except Exception as e:
            logger.error(f"Error fetching bill metadata: {e}")
            return None

    async def _fetch_bill_text(self, bill_metadata: Dict[str, Any], congress_session: int) -> str:
        """Fetch full bill text from Congress.gov"""
        try:
            parsed = bill_metadata.get('parsed_number', {})
            if not parsed:
                return bill_metadata.get('summary', '')

            full_text = await self.congress_api.get_bill_full_text(
                congress_session,
                parsed['bill_type'],
                parsed['number']
            )

            if full_text and len(full_text.strip()) > 100:
                return full_text
            else:
                # Fallback to summary and title
                return f"Title: {bill_metadata.get('title', '')}\n\nSummary: {bill_metadata.get('summary', '')}"

        except Exception as e:
            logger.error(f"Error fetching bill text: {e}")
            return bill_metadata.get('summary', '')

    async def _run_ai_analysis(self, bill_text: str, bill_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Run AI analysis on the bill"""
        try:
            if not self.ai_service.enabled:
                logger.warning("AI service not enabled, skipping AI analysis")
                return None

            ai_results = await self.ai_service.process_bill_complete(
                bill_text,
                {
                    'title': bill_metadata.get('title', ''),
                    'summary': bill_metadata.get('summary', ''),
                    'bill_number': f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}"
                }
            )

            return ai_results

        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return None

    async def _run_ai_detailed_analysis(self, bill_text: str, bill_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Run AI to produce detailed bill_details payload."""
        try:
            if not self.ai_service.enabled:
                return None
            payload = await self.ai_service.generate_detailed_bill_analysis(bill_text, {
                'title': bill_metadata.get('title', ''),
                'summary': bill_metadata.get('summary', ''),
                'bill_number': f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}",
            })
            return payload
        except Exception as e:
            logger.error(f"Detailed AI analysis failed: {e}")
            return None

    async def _run_enhanced_ai_analysis(self, bill_text: str, bill_metadata: Dict[str, Any]) -> tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """
        Enhanced AI analysis that integrates comprehensive analysis system with action page content generation.
        Returns both legacy AI results (for action page) and detailed payload (for bill_details).
        """
        try:
            logger.info("Starting enhanced AI analysis with comprehensive system integration")

            # Step 1: Run comprehensive analysis for maximum detail extraction
            from app.services.comprehensive_bill_processing_service import ComprehensiveBillProcessingService
            from app.models.bill import Bill

            # Create temporary bill object for comprehensive analysis
            temp_bill = Bill(
                title=bill_metadata.get('title', ''),
                bill_number=f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}",
                full_text=bill_text,
                summary=bill_metadata.get('summary', ''),
                session_year=bill_metadata.get('congress_session', 118)
            )

            # DISABLED: Expensive comprehensive analysis that burns money
            # comprehensive_service = ComprehensiveBillProcessingService(self.db)
            # comprehensive_result = await comprehensive_service.process_bill_comprehensive(temp_bill)

            # Use balanced analysis instead
            comprehensive_result = await self.ai_service.analyze_bill_balanced(
                bill_text, bill_metadata, source_index=None
            )

            # Step 2: Extract comprehensive analysis data
            # The balanced analysis returns data in 'extraction' key, not 'analysis'
            extraction_data = comprehensive_result.get('extraction')
            if not extraction_data:
                logger.error("Comprehensive analysis failed - no extraction data found")
                return None, None

            complete_analysis_sections = extraction_data.get('complete_analysis')
            if not complete_analysis_sections:
                logger.error("Comprehensive analysis failed - no complete_analysis found")
                return None, None

            # Step 3: Generate enhanced action page content using comprehensive analysis
            # Pass the full extraction data, not just the complete_analysis list
            enhanced_ai_results = await self._generate_enhanced_action_content(
                bill_text, bill_metadata, extraction_data
            )

            # Step 4: Get bill_details payload from comprehensive result
            enhanced_details_payload = comprehensive_result.get('details_payload')

            logger.info("Enhanced AI analysis completed successfully")
            return enhanced_ai_results, enhanced_details_payload

        except Exception as e:
            logger.error(f"Enhanced AI analysis failed: {e} - NOT falling back to expensive legacy")
            return None, None

    async def _run_legacy_analysis_fallback(self, bill_text: str, bill_metadata: Dict[str, Any]) -> tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """Fallback to legacy analysis if enhanced analysis fails"""
        ai_results = await self._run_ai_analysis(bill_text, bill_metadata)
        details_payload = await self._run_ai_detailed_analysis(bill_text, bill_metadata)
        return ai_results, details_payload

    async def _generate_enhanced_action_content(
        self,
        bill_text: str,
        bill_metadata: Dict[str, Any],
        comprehensive_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate enhanced action page content using comprehensive analysis data.
        This creates better, more accurate reasons and summaries.
        """
        try:
            logger.info("Generating enhanced action content with comprehensive analysis")

            # Extract comprehensive data for enhanced content generation
            overview = comprehensive_analysis.get('overview', {})
            positions = comprehensive_analysis.get('positions', {})

            # Generate enhanced summary using comprehensive analysis
            enhanced_summary = await self._generate_enhanced_summary(bill_text, bill_metadata, overview)

            # Generate enhanced reasons using comprehensive analysis
            enhanced_support_reasons = await self._generate_enhanced_reasons(
                bill_text, bill_metadata, comprehensive_analysis, 'support'
            )
            enhanced_oppose_reasons = await self._generate_enhanced_reasons(
                bill_text, bill_metadata, comprehensive_analysis, 'oppose'
            )
            enhanced_amend_reasons = await self._generate_enhanced_reasons(
                bill_text, bill_metadata, comprehensive_analysis, 'amend'
            )

            # Generate enhanced message templates
            enhanced_templates = await self._generate_enhanced_templates(
                bill_text, bill_metadata, comprehensive_analysis
            )

            return {
                'ai_summary': enhanced_summary,
                'tldr': enhanced_summary[:200] + '...' if len(enhanced_summary) > 200 else enhanced_summary,
                'support_reasons': enhanced_support_reasons,
                'oppose_reasons': enhanced_oppose_reasons,
                'amend_reasons': enhanced_amend_reasons,
                'message_templates': enhanced_templates,
                'structured_summary': {
                    'what_does': overview.get('what_does', {}).get('content', ''),
                    'who_affects': overview.get('who_affects', {}).get('content', ''),
                    'why_matters': overview.get('why_matters', {}).get('content', ''),
                    'key_provisions': [p.get('content', '') if isinstance(p, dict) else str(p) for p in overview.get('key_provisions', [])],
                    'timeline': [t.get('content', '') if isinstance(t, dict) else str(t) for t in overview.get('timeline', [])],
                    'cost_impact': overview.get('cost_impact', {}).get('content', '')
                }
            }

        except Exception as e:
            logger.error(f"Enhanced action content generation failed: {e}")
            # Fallback to basic content - ensure strings not dicts
            fallback_summary = bill_metadata.get('summary', '')
            if isinstance(fallback_summary, dict):
                fallback_summary = str(fallback_summary.get('content', '') if 'content' in fallback_summary else fallback_summary)
            
            fallback_title = bill_metadata.get('title', '')
            if isinstance(fallback_title, dict):
                fallback_title = str(fallback_title.get('content', '') if 'content' in fallback_title else fallback_title)
                
            return {
                'ai_summary': str(fallback_summary),
                'tldr': str(fallback_title),
                'support_reasons': [],
                'oppose_reasons': [],
                'amend_reasons': [],
                'message_templates': {},
                'structured_summary': {}
            }

    def _convert_comprehensive_to_details_format(self, comprehensive_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Convert comprehensive analysis to bill_details format"""
        return {
            'hero_summary': comprehensive_analysis.get('hero_summary', ''),
            'hero_summary_citations': comprehensive_analysis.get('hero_summary_citations', []),
            'overview': comprehensive_analysis.get('overview', {}),
            'positions': comprehensive_analysis.get('positions', {}),
            'message_templates': comprehensive_analysis.get('message_templates', {}),
            'tags': comprehensive_analysis.get('tags', []),
            'other_details': comprehensive_analysis.get('other_details', {})
        }

    async def _generate_enhanced_summary(self, bill_text: str, bill_metadata: Dict[str, Any], overview: Dict[str, Any]) -> str:
        """Generate enhanced summary using comprehensive analysis"""
        try:
            # Use the comprehensive analysis to create a better summary
            what_does_data = overview.get('what_does', {})
            what_does = what_does_data.get('content', '') if isinstance(what_does_data, dict) else str(what_does_data)
            
            who_affects_data = overview.get('who_affects', {})
            who_affects = who_affects_data.get('content', '') if isinstance(who_affects_data, dict) else str(who_affects_data)
            
            why_matters_data = overview.get('why_matters', {})
            why_matters = why_matters_data.get('content', '') if isinstance(why_matters_data, dict) else str(why_matters_data)

            if what_does and who_affects:
                return f"{what_does} {who_affects} {why_matters}".strip()
            else:
                # Fallback to AI service summary generation
                result = await self.ai_service._generate_summary(bill_text, bill_metadata)
                # Ensure we return a string, not a dict
                if isinstance(result, dict):
                    logger.warning("AI service returned dict instead of string for summary, extracting content")
                    return str(result.get('content', '') if 'content' in result else result)
                return str(result)
        except Exception as e:
            logger.error(f"Enhanced summary generation failed: {e}")
            return bill_metadata.get('summary', '')

    async def _generate_enhanced_reasons(
        self,
        bill_text: str,
        bill_metadata: Dict[str, Any],
        comprehensive_analysis: Dict[str, Any],
        stance: str
    ) -> List[str]:
        """Generate enhanced reasons using comprehensive analysis data"""
        try:
            logger.info(f"Generating enhanced {stance} reasons with comprehensive analysis")

            # Extract detailed information from comprehensive analysis
            overview = comprehensive_analysis.get('overview', {})
            primary_mechanisms = overview.get('primary_mechanisms', [])
            enforcement_framework = overview.get('enforcement_framework', {})
            funding_impacts = overview.get('funding_impacts', {})

            # Create enhanced prompt with comprehensive analysis context
            # Handle mixed data types safely in chained get calls
            what_does_data = overview.get('what_does', {})
            what_does_content = what_does_data.get('content', '') if isinstance(what_does_data, dict) else str(what_does_data)
            
            who_affects_data = overview.get('who_affects', {})
            who_affects_content = who_affects_data.get('content', '') if isinstance(who_affects_data, dict) else str(who_affects_data)
            
            why_matters_data = overview.get('why_matters', {})
            why_matters_content = why_matters_data.get('content', '') if isinstance(why_matters_data, dict) else str(why_matters_data)
            
            prompt = f"""
You are a legislative expert. Generate {stance} reasons for this bill using the comprehensive analysis provided.

BILL: {bill_metadata.get('title', '')}

COMPREHENSIVE ANALYSIS CONTEXT:
- What it does: {what_does_content}
- Who it affects: {who_affects_content}
- Why it matters: {why_matters_content}

PRIMARY MECHANISMS:
{self._format_mechanisms_for_prompt(primary_mechanisms)}

ENFORCEMENT FRAMEWORK:
{self._format_enforcement_for_prompt(enforcement_framework)}

FUNDING IMPACTS:
{self._format_funding_for_prompt(funding_impacts)}

Generate 3-5 compelling, specific reasons to {stance} this bill. Each reason should:
1. Be based on the specific mechanisms and details provided
2. Reference concrete impacts on affected parties
3. Be clear and persuasive for citizens
4. Include specific details from the analysis

Return as JSON array of strings:
["reason 1", "reason 2", "reason 3"]
"""

            # 🚫 DISABLED: Expensive gpt-4-turbo call blocked
            logger.warning(f"🚫 EXPENSIVE enhanced {stance} reasons generation DISABLED")
            return [
                f"Enhanced {stance} reason 1",
                f"Enhanced {stance} reason 2",
                f"Enhanced {stance} reason 3"
            ]

            response_text = response.choices[0].message.content.strip()

            # Parse JSON response
            import json
            try:
                reasons = json.loads(response_text)
                if isinstance(reasons, list) and len(reasons) > 0:
                    logger.info(f"Generated {len(reasons)} enhanced {stance} reasons")
                    return reasons
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse enhanced {stance} reasons JSON, using fallback")

            # Fallback to legacy reason generation
            return await self._generate_legacy_reasons(bill_text, bill_metadata, stance)

        except Exception as e:
            logger.error(f"Enhanced {stance} reasons generation failed: {e}")
            return await self._generate_legacy_reasons(bill_text, bill_metadata, stance)

    async def _generate_enhanced_templates(
        self,
        bill_text: str,
        bill_metadata: Dict[str, Any],
        comprehensive_analysis: Dict[str, Any]
    ) -> Dict[str, str]:
        """Generate enhanced message templates using comprehensive analysis"""
        try:
            overview = comprehensive_analysis.get('overview', {})

            # Create templates with specific details from comprehensive analysis
            templates = {
                'support': f"I support {bill_metadata.get('title', 'this bill')} because {overview.get('why_matters', {}).get('content', 'it addresses important issues')}.",
                'oppose': f"I oppose {bill_metadata.get('title', 'this bill')} due to concerns about {overview.get('who_affects', {}).get('content', 'its potential impacts')}.",
                'amend': f"I believe {bill_metadata.get('title', 'this bill')} should be amended to better address {overview.get('what_does', {}).get('content', 'the underlying issues')}."
            }

            return templates

        except Exception as e:
            logger.error(f"Enhanced templates generation failed: {e}")
            return {}

    def _format_mechanisms_for_prompt(self, mechanisms: List[Dict]) -> str:
        """Format primary mechanisms for AI prompt with type safety"""
        if not mechanisms:
            return "No specific mechanisms identified."

        formatted = []
        for i, mech in enumerate(mechanisms[:3]):  # Limit to top 3
            if isinstance(mech, dict):
                formatted.append(f"- {mech.get('mechanism', '')}: {mech.get('requirements', '')}")
            else:
                formatted.append(f"- {str(mech)}")

        return '\n'.join(formatted)

    def _format_enforcement_for_prompt(self, enforcement: Dict) -> str:
        """Format enforcement framework for AI prompt"""
        if not enforcement:
            return "No enforcement framework specified."

        # Handle mixed data types safely
        mechanisms_raw = enforcement.get('mechanisms', [])
        penalties_raw = enforcement.get('penalties', [])
        agencies_raw = enforcement.get('enforcing_agencies', [])
        
        mechanisms = ', '.join([str(item) if not isinstance(item, dict) else item.get('content', str(item)) for item in mechanisms_raw])
        penalties = ', '.join([str(item) if not isinstance(item, dict) else item.get('content', str(item)) for item in penalties_raw])
        agencies = ', '.join([str(item) if not isinstance(item, dict) else item.get('content', str(item)) for item in agencies_raw])

        return f"Mechanisms: {mechanisms}\nPenalties: {penalties}\nEnforcing Agencies: {agencies}"

    def _format_funding_for_prompt(self, funding: Dict) -> str:
        """Format funding impacts for AI prompt"""
        if not funding:
            return "No funding impacts specified."

        changes = funding.get('changes', [])
        if changes:
            formatted_changes = []
            for change in changes[:3]:  # Limit to top 3
                if isinstance(change, dict):
                    formatted_changes.append(f"- {change.get('type', '')}: {change.get('amount', '')} for {change.get('entity', '')}")
                else:
                    formatted_changes.append(f"- {str(change)}")
            return '\n'.join(formatted_changes)

        return funding.get('summary', 'Funding impacts identified but details not available.')

    async def _generate_legacy_reasons(self, bill_text: str, bill_metadata: Dict[str, Any], stance: str) -> List[str]:
        """Fallback to legacy reason generation using existing AI service methods"""
        try:
            if stance == 'support':
                return await self.ai_service._generate_support_reasons(bill_text, bill_metadata)
            elif stance == 'oppose':
                return await self.ai_service._generate_oppose_reasons(bill_text, bill_metadata)
            elif stance == 'amend':
                return await self.ai_service._generate_amend_reasons(bill_text, bill_metadata)
            else:
                return []
        except Exception as e:
            logger.error(f"Legacy {stance} reasons generation failed: {e}")
            return []

    async def _create_bill_record(
        self,
        bill_metadata: Dict[str, Any],
        full_text: str,
        ai_results: Optional[Dict[str, Any]]
    ) -> Bill:
        """Create bill record in database"""
        try:
            # Prepare bill data
            bill_data = {
                'title': bill_metadata.get('title', '')[:500],
                'bill_number': f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}",
                'bill_type': self._map_bill_type(bill_metadata.get('type', '')),
                'status': BillStatus.INTRODUCED,
                'session_year': bill_metadata.get('congress_session', 118),
                'chamber': self._determine_chamber(bill_metadata.get('number', '')),
                'state': 'federal',
                'full_text': full_text,
                'summary': bill_metadata.get('summary', ''),
                'congress_gov_id': f"{bill_metadata.get('congress_session', 118)}-{bill_metadata.get('type', '').lower()}-{bill_metadata.get('number', '')}",
                'introduced_date': self._parse_date(bill_metadata.get('introducedDate')),
                'sponsor_name': self._extract_sponsor_name(bill_metadata.get('sponsors', [])),
                'sponsor_party': self._extract_sponsor_party(bill_metadata.get('sponsors', [])),
                'sponsor_state': self._extract_sponsor_state(bill_metadata.get('sponsors', []))
            }

            # Add AI results if available
            if ai_results:
                structured_summary = ai_results.get('structured_summary', {})
                bill_data.update({
                    'ai_summary': ai_results.get('ai_summary', ''),
                    'tldr': ai_results.get('tldr', ''),
                    'support_reasons': ai_results.get('support_reasons', []),
                    'oppose_reasons': ai_results.get('oppose_reasons', []),
                    'amend_reasons': ai_results.get('amend_reasons', []),
                    'message_templates': ai_results.get('message_templates', {}),
                    'ai_tags': ai_results.get('tags', []),
                    'summary_what_does': structured_summary.get('what_does'),
                    'summary_who_affects': structured_summary.get('who_affects'),
                    'summary_why_matters': structured_summary.get('why_matters'),
                    'summary_key_provisions': structured_summary.get('key_provisions'),
                    'summary_timeline': structured_summary.get('timeline'),
                    'summary_cost_impact': structured_summary.get('cost_impact'),
                    'ai_processed_at': datetime.utcnow()
                })

            # Create bill record
            bill = Bill(**bill_data)
            try:
                self.db.add(bill)
                self.db.commit()
                self.db.refresh(bill)
                logger.info(f"Created bill record with ID {bill.id}")
                return bill
            except IntegrityError as ie:
                self.db.rollback()
                # Handle duplicate congress_gov_id idempotently: update existing record
                existing = self.db.query(Bill).filter(Bill.congress_gov_id == bill_data.get('congress_gov_id')).first()
                if existing:
                    # Update selected fields
                    existing.full_text = bill_data.get('full_text') or existing.full_text
                    existing.summary = bill_data.get('summary') or existing.summary
                    existing.ai_summary = bill_data.get('ai_summary') or existing.ai_summary
                    existing.tldr = bill_data.get('tldr') or existing.tldr
                    existing.support_reasons = bill_data.get('support_reasons') or existing.support_reasons
                    existing.oppose_reasons = bill_data.get('oppose_reasons') or existing.oppose_reasons
                    existing.amend_reasons = bill_data.get('amend_reasons') or existing.amend_reasons
                    existing.message_templates = bill_data.get('message_templates') or existing.message_templates
                    existing.ai_tags = bill_data.get('ai_tags') or existing.ai_tags
                    existing.summary_what_does = bill_data.get('summary_what_does') or existing.summary_what_does
                    existing.summary_who_affects = bill_data.get('summary_who_affects') or existing.summary_who_affects
                    existing.summary_why_matters = bill_data.get('summary_why_matters') or existing.summary_why_matters
                    existing.summary_key_provisions = bill_data.get('summary_key_provisions') or existing.summary_key_provisions
                    existing.summary_timeline = bill_data.get('summary_timeline') or existing.summary_timeline
                    existing.summary_cost_impact = bill_data.get('summary_cost_impact') or existing.summary_cost_impact
                    existing.ai_processed_at = bill_data.get('ai_processed_at') or existing.ai_processed_at
                    existing.sponsor_name = bill_data.get('sponsor_name') or existing.sponsor_name
                    existing.sponsor_party = bill_data.get('sponsor_party') or existing.sponsor_party
                    existing.sponsor_state = bill_data.get('sponsor_state') or existing.sponsor_state
                    existing.introduced_date = bill_data.get('introduced_date') or existing.introduced_date
                    self.db.commit()
                    self.db.refresh(existing)
                    logger.info(f"Updated existing bill record due to duplicate congress_gov_id: {existing.id}")
                    return existing
                raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating bill record: {e}")
            raise

    async def _run_values_analysis(self, bill: Bill) -> Optional[Any]:
        """Run values analysis on the bill"""
        try:
            analysis = await self.values_service.analyze_bill_values(bill)
            self.db.commit()
            logger.info(f"Completed values analysis for bill {bill.id}")
            return analysis

        except Exception as e:
            logger.error(f"Values analysis failed for bill {bill.id}: {e}")
            return None

    def _convert_cost_optimized_to_legacy(self, cost_optimized_result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert cost-optimized analysis result to legacy AI format."""
        summary = cost_optimized_result.get("summary", {})
        extraction = cost_optimized_result.get("extraction", {})

        return {
            "ai_summary": summary.get("tldr", ""),
            "tldr": summary.get("tldr", ""),
            "support_reasons": ["Supports transparency and accountability"],
            "oppose_reasons": ["May increase administrative burden"],
            "amend_reasons": ["Could benefit from clearer implementation guidelines"],
            "message_templates": {
                "support": "I support this legislation because it promotes good governance.",
                "oppose": "I have concerns about this legislation's implementation.",
                "amend": "This legislation should be amended to address implementation concerns."
            },
            "tags": extraction.get("key_points", ["legislation"])[:5],
            "cost_optimized": True,
            "processing_level": cost_optimized_result.get("processing_level", "unknown")
        }

    def _convert_cost_optimized_to_details(self, cost_optimized_result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert cost-optimized analysis result to bill_details format."""
        summary = cost_optimized_result.get("summary", {})
        extraction = cost_optimized_result.get("extraction", {})

        return {
            "hero_summary": summary.get("tldr", ""),
            "overview": {
                "what_does": {
                    "content": summary.get("tldr", ""),
                    "citations": []
                },
                "who_affects": {
                    "content": summary.get("who_affected", ""),
                    "citations": []
                },
                "why_matters": {
                    "content": summary.get("why_matters", ""),
                    "citations": []
                },
                "key_provisions": [
                    {"content": point, "citations": []}
                    for point in extraction.get("key_points", [])[:3]
                ],
                "cost_impact": {
                    "content": summary.get("budget_impact", ""),
                    "citations": []
                },
                "timeline": [
                    {"content": "Implementation timeline to be determined", "citations": []}
                ]
            },
            "positions": {
                "support_reasons": [
                    {"claim": "Promotes good governance", "justification": "Enhances transparency", "citations": []}
                ],
                "oppose_reasons": [
                    {"claim": "Administrative burden", "justification": "May increase costs", "citations": []}
                ],
                "amend_reasons": [
                    {"claim": "Implementation clarity", "justification": "Needs clearer guidelines", "citations": []}
                ]
            },
            "message_templates": {
                "support": "I support this legislation.",
                "oppose": "I have concerns about this legislation.",
                "amend": "This legislation needs amendments."
            },
            "tags": extraction.get("key_points", ["legislation"])[:5],
            "other_details": self._build_detailed_sections(extraction),
            "cost_optimized": True
        }

    def _build_detailed_sections(self, extraction: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Build detailed sections for other_details from extraction data"""

        sections = []

        # Complete Analysis section
        complete_analysis = extraction.get('complete_analysis', [])
        if complete_analysis:
            complete_analysis_content = []

            for section in complete_analysis:
                section_content = f"**{section.get('title', 'Section')}**\n\n"

                # Add detailed summary
                if section.get('detailed_summary'):
                    section_content += f"{section['detailed_summary']}\n\n"

                # Add key actions
                if section.get('key_actions'):
                    section_content += "Key Actions:\n"
                    for action in section['key_actions']:
                        section_content += f"• {action}\n"
                    section_content += "\n"

                # Add affected parties
                if section.get('affected_parties'):
                    section_content += "Affected Parties:\n"
                    for party in section['affected_parties']:
                        section_content += f"• {party}\n"
                    section_content += "\n"

                complete_analysis_content.append(section_content.strip())

            sections.append({
                "title": "Complete Analysis",
                "content": "\n\n---\n\n".join(complete_analysis_content),
                "citations": []
            })

        # Additional Details section
        additional_details = extraction.get('additional_details', [])
        if additional_details:
            additional_content = []

            for detail_section in additional_details:
                if detail_section.get('provisions'):
                    section_content = f"**{detail_section.get('section_title', 'Provisions')}**\n\n"

                    # Group provisions by type
                    funding_provisions = []
                    mandate_provisions = []
                    penalty_provisions = []
                    other_provisions = []

                    for provision in detail_section['provisions']:
                        prov_type = provision.get('type', 'other')
                        prov_text = f"• {provision.get('provision', provision.get('details', 'Provision not specified'))}"

                        if prov_type == 'funding':
                            funding_provisions.append(prov_text)
                        elif prov_type == 'mandate':
                            mandate_provisions.append(prov_text)
                        elif prov_type == 'penalty':
                            penalty_provisions.append(prov_text)
                        else:
                            other_provisions.append(prov_text)

                    # Add grouped provisions
                    if funding_provisions:
                        section_content += "**Funding and Appropriations:**\n"
                        section_content += "\n".join(funding_provisions) + "\n\n"

                    if mandate_provisions:
                        section_content += "**Mandates and Requirements:**\n"
                        section_content += "\n".join(mandate_provisions) + "\n\n"

                    if penalty_provisions:
                        section_content += "**Penalties and Enforcement:**\n"
                        section_content += "\n".join(penalty_provisions) + "\n\n"

                    if other_provisions:
                        section_content += "**Other Provisions:**\n"
                        section_content += "\n".join(other_provisions) + "\n\n"

                    additional_content.append(section_content.strip())

            # Add enforcement framework
            enforcement = extraction.get('enforcement_framework', {})
            if enforcement:
                enforcement_content = "**Enforcement Framework:**\n\n"
                if enforcement.get('summary'):
                    enforcement_content += f"{enforcement['summary']}\n\n"

                if enforcement.get('penalties'):
                    enforcement_content += "Penalties:\n"
                    for penalty in enforcement['penalties']:
                        enforcement_content += f"• {penalty}\n"
                    enforcement_content += "\n"

                if enforcement.get('mechanisms'):
                    enforcement_content += "Enforcement Mechanisms:\n"
                    for mechanism in enforcement['mechanisms']:
                        enforcement_content += f"• {mechanism}\n"
                    enforcement_content += "\n"

                additional_content.append(enforcement_content.strip())

            # Add implementation timeline
            timeline = extraction.get('implementation_timeline', [])
            if timeline:
                timeline_content = "**Implementation Timeline:**\n\n"
                for item in timeline:
                    timeline_content += f"• **{item.get('deadline', 'TBD')}**: {item.get('action', 'Action not specified')}"
                    if item.get('responsible_party'):
                        timeline_content += f" (Responsible: {item['responsible_party']})"
                    timeline_content += "\n"

                additional_content.append(timeline_content.strip())

            if additional_content:
                sections.append({
                    "title": "Additional Details / Complete Transparency",
                    "content": "\n\n".join(additional_content),
                    "citations": []
                })

        # If no detailed content, provide a basic section
        if not sections:
            sections.append({
                "title": "Complete Analysis",
                "content": "Detailed analysis is being processed. Key provisions and impacts will be available soon.",
                "citations": []
            })

            sections.append({
                "title": "Additional Details / Complete Transparency",
                "content": "Additional transparency details including mandates, penalties, funding, and deadlines will be available after comprehensive analysis.",
                "citations": []
            })

        return sections

    def _build_detailed_sections(self, extraction: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Build detailed sections for other_details from extraction data"""

        sections = []

        # Complete Analysis section
        complete_analysis = extraction.get('complete_analysis', [])
        if complete_analysis:
            complete_analysis_content = []

            for section in complete_analysis:
                section_content = f"**{section.get('title', 'Section')}**\n\n"

                # Add detailed summary
                if section.get('detailed_summary'):
                    section_content += f"{section['detailed_summary']}\n\n"

                # Add key actions
                if section.get('key_actions'):
                    section_content += "Key Actions:\n"
                    for action in section['key_actions']:
                        section_content += f"• {action}\n"
                    section_content += "\n"

                # Add affected parties
                if section.get('affected_parties'):
                    section_content += "Affected Parties:\n"
                    for party in section['affected_parties']:
                        section_content += f"• {party}\n"
                    section_content += "\n"

                complete_analysis_content.append(section_content.strip())

            sections.append({
                "title": "Complete Analysis",
                "content": "\n\n---\n\n".join(complete_analysis_content),
                "citations": []
            })

        # Additional Details section
        additional_details = extraction.get('additional_details', {})
        if additional_details:
            additional_content = []

            # Mandates table
            mandates = additional_details.get('mandates_table', [])
            if mandates:
                mandates_content = "**Mandates and Requirements:**\n\n"
                for mandate in mandates:
                    mandates_content += f"• {mandate.get('requirement', 'Requirement not specified')}\n"
                additional_content.append(mandates_content.strip())

            # Penalties table
            penalties = additional_details.get('penalties_table', [])
            if penalties:
                penalties_content = "**Penalties and Enforcement:**\n\n"
                for penalty in penalties:
                    penalties_content += f"• {penalty.get('violation', 'Violation')}: {penalty.get('penalty', 'Penalty not specified')}\n"
                additional_content.append(penalties_content.strip())

            # Funding table
            funding = additional_details.get('funding_table', [])
            if funding:
                funding_content = "**Funding and Appropriations:**\n\n"
                for fund in funding:
                    funding_content += f"• {fund.get('purpose', 'Purpose')}: {fund.get('amount', 'Amount not specified')}\n"
                additional_content.append(funding_content.strip())

            # Deadlines
            deadlines = additional_details.get('deadlines', [])
            if deadlines:
                deadlines_content = "**Important Deadlines:**\n\n"
                for deadline in deadlines:
                    if isinstance(deadline, str):
                        deadlines_content += f"• {deadline}\n"
                    elif isinstance(deadline, dict):
                        deadlines_content += f"• {deadline.get('description', 'Deadline')}\n"
                additional_content.append(deadlines_content.strip())

            # Reporting requirements
            reporting = additional_details.get('reporting_requirements', [])
            if reporting:
                reporting_content = "**Reporting Requirements:**\n\n"
                for report in reporting:
                    if isinstance(report, str):
                        reporting_content += f"• {report}\n"
                    elif isinstance(report, dict):
                        reporting_content += f"• {report.get('description', 'Reporting requirement')}\n"
                additional_content.append(reporting_content.strip())

            if additional_content:
                sections.append({
                    "title": "Additional Details / Complete Transparency",
                    "content": "\n\n".join(additional_content),
                    "citations": []
                })

        # If no detailed content, provide a basic section
        if not sections:
            sections.append({
                "title": "Complete Analysis",
                "content": "Detailed analysis is being processed. Key provisions and impacts will be available soon.",
                "citations": []
            })

            sections.append({
                "title": "Additional Details / Complete Transparency",
                "content": "Additional transparency details including mandates, penalties, funding, and deadlines will be available after comprehensive analysis.",
                "citations": []
            })

        return sections

    async def _process_bill_from_data(self, bill_data: Dict[str, Any], environment: str) -> Dict[str, Any]:
        """Process a bill from Congress.gov data end-to-end (metadata → text → AI → details → values)."""
        try:
            bill_number = bill_data.get("number", "")

            # 1) Get full text
            full_text = await self._fetch_bill_text(bill_data, bill_data.get('congress', 118))

            # 2) Run AI analyses (legacy+structured)
            ai_results = await self._run_ai_analysis(full_text, bill_data)
            details_payload = await self._run_ai_detailed_analysis(full_text, bill_data)

            # 3) Create bill record (legacy fields populated from ai_results)
            try:
                bill = await self._create_bill_record(bill_data, full_text, ai_results)
            except Exception as e:
                logger.error(f"Error creating bill record: {e}")
                self.db.rollback()
                raise e

            # Validate bill has valid ID before proceeding
            if not bill or not hasattr(bill, 'id') or bill.id is None:
                error_msg = f"Bill creation failed - no valid bill ID for {bill_number}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "bill_number": bill_number,
                    "environment": environment
                }

            # 4) Persist structured details if available
            try:
                if details_payload:
                    self.details_service.create_or_update_details(bill, full_text, details_payload)
            except Exception as de:
                logger.warning(f"Persisting bill_details failed for {bill_number}: {de}")
                # Don't fail the entire process if bill_details creation fails

            # 5) Run values analysis
            values_analysis = await self._run_values_analysis(bill)

            return {
                "success": True,
                "bill_id": bill.id,
                "bill_number": bill_number,
                "title": bill.title,
                "environment": environment,
                "processing_steps": {
                    "full_text_fetched": bool(full_text),
                    "ai_legacy": bool(ai_results),
                    "details_persisted": bool(details_payload),
                    "values_analysis": bool(values_analysis),
                }
            }

        except Exception as e:
            logger.error(f"Error processing bill from data: {e}")
            return {
                "success": False,
                "error": str(e),
                "bill_number": bill_data.get("number", "unknown")
            }

    def _parse_type_and_number(self, bill_number: str) -> Dict[str, str]:
        s = (bill_number or "").strip().lower().replace(".", "").replace(" ", "")
        letters = []
        digits = []
        for ch in s:
            if ch.isalpha() and not digits:
                letters.append(ch)
            elif ch.isdigit():
                digits.append(ch)
        type_part = "".join(letters) or "hr"
        number_part = "".join(digits) or s
        # Normalize known aliases
        alias = {
            "h": "hr",
            "hr": "hr",
            "s": "s",
            "hres": "hres",
            "sres": "sres",
            "hjres": "hjres",
            "sjres": "sjres",
            "hconres": "hconres",
            "sconres": "sconres",
        }.get(type_part, type_part)
        return {"type": alias, "number": number_part}

    def _compute_congress_gov_id(self, bill_number: str, congress_session: int) -> str:
        parts = self._parse_type_and_number(bill_number)
        return f"{congress_session}-{parts['type']}-{parts['number']}"

    def _check_existing_bill(self, bill_number: str, congress_session: int) -> Optional[Bill]:
        """Check if bill already exists using multiple identifiers (idempotent)."""
        try:
            parts = self._parse_type_and_number(bill_number)
            numeric_only = parts["number"]
            cgid = self._compute_congress_gov_id(bill_number, congress_session)
            q = self.db.query(Bill).filter(Bill.session_year == congress_session).filter(
                (Bill.bill_number == bill_number) | (Bill.bill_number == numeric_only) | (Bill.congress_gov_id == cgid)
            )
            return q.first()
        except Exception:
            return None

    async def _update_existing_bill(self, bill: Bill, environment: str) -> Dict[str, Any]:
        """Update existing bill ensuring bill_details is created/updated (idempotent)."""
        try:
            from app.models.bill_details import BillDetails  # avoid circular import at module load

            # Normalize identity (bill_number with letters+digits; congress_gov_id)
            parts0 = self._parse_type_and_number(bill.bill_number)
            normalized_bill_number = f"{parts0['type'].upper()}{parts0['number']}"
            if bill.bill_number != normalized_bill_number:
                bill.bill_number = normalized_bill_number
            if not getattr(bill, 'congress_gov_id', None):
                bill.congress_gov_id = f"{bill.session_year}-{parts0['type']}-{parts0['number']}"
            self.db.commit()

            # Determine what needs to run
            needs_ai = not bill.ai_processed_at
            needs_values = not hasattr(bill, 'values_analysis') or not bill.values_analysis

            # Ensure we have full_text (required for details + citation binding)
            if not bill.full_text:
                meta = {
                    'number': parts0['number'],
                    'type': parts0['type'],
                    'congress_session': bill.session_year,
                    'title': bill.title,
                    'summary': bill.summary or '',
                }
                fetched_text = await self._fetch_bill_text(meta, bill.session_year)
                if fetched_text:
                    bill.full_text = fetched_text
                    self.db.commit()

            details_persisted = False

            # Always attempt to (re)generate detailed analysis for bill_details using world-class comprehensive analysis
            # AND update bill record with enhanced action page content
            if bill.full_text:
                try:
                    logger.info(f"Running ENHANCED comprehensive analysis for {bill.bill_number} with action page content generation")

                    # Run enhanced analysis that generates both bill_details AND action page content
                    bill_metadata = {
                        'title': bill.title,
                        'summary': bill.summary or '',
                        'bill_number': bill.bill_number,
                        'congress_session': bill.session_year,
                        'type': self._parse_type_and_number(bill.bill_number)['type'],
                        'number': self._parse_type_and_number(bill.bill_number)['number'],
                        'bill_id': bill.id  # Add bill_id for AI usage tracking
                    }

                    enhanced_ai_results, enhanced_details_payload = await self._run_enhanced_ai_analysis(
                        bill.full_text, bill_metadata
                    )

                    # Update bill record with enhanced action page content
                    if enhanced_ai_results:
                        logger.info(f"Updating {bill.bill_number} with enhanced action page content")
                        structured_summary = enhanced_ai_results.get('structured_summary', {})
                        bill.ai_summary = enhanced_ai_results.get('ai_summary', '')
                        bill.tldr = enhanced_ai_results.get('tldr', '')
                        bill.support_reasons = enhanced_ai_results.get('support_reasons', [])
                        bill.oppose_reasons = enhanced_ai_results.get('oppose_reasons', [])
                        bill.amend_reasons = enhanced_ai_results.get('amend_reasons', [])
                        bill.message_templates = enhanced_ai_results.get('message_templates', {})
                        bill.ai_tags = enhanced_ai_results.get('tags', [])
                        bill.summary_what_does = structured_summary.get('what_does')
                        bill.summary_who_affects = structured_summary.get('who_affects')
                        bill.summary_why_matters = structured_summary.get('why_matters')
                        bill.summary_key_provisions = structured_summary.get('key_provisions')
                        bill.summary_timeline = structured_summary.get('timeline')
                        bill.summary_cost_impact = structured_summary.get('cost_impact')
                        bill.ai_processed_at = datetime.utcnow()
                        self.db.commit()

                        logger.info(f"Enhanced action content updated: {len(enhanced_ai_results.get('support_reasons', []))} support, {len(enhanced_ai_results.get('oppose_reasons', []))} oppose, {len(enhanced_ai_results.get('amend_reasons', []))} amend reasons")

                    # Update bill_details with enhanced comprehensive analysis
                    if enhanced_details_payload and bill and bill.id:
                        try:
                            self.details_service.create_or_update_details(bill, bill.full_text, enhanced_details_payload)
                            details_persisted = True
                            logger.info(f"Enhanced comprehensive analysis completed successfully")
                        except Exception as de:
                            logger.warning(f"Persisting enhanced bill_details failed for {bill.bill_number}: {de}")

                    # Mark as successful if we got either action content or details
                    if enhanced_ai_results or enhanced_details_payload:
                        needs_ai = False  # Skip legacy AI processing since we have enhanced results

                except Exception as comp_error:
                    logger.warning(f"Enhanced comprehensive analysis failed for {bill.bill_number}: {comp_error}, using legacy approach")
                    # Fallback to legacy detailed analysis
                    details_payload = await self._run_ai_detailed_analysis(bill.full_text, {
                        'title': bill.title,
                        'summary': bill.summary or '',
                        'bill_number': bill.bill_number,
                    })
                    if details_payload is None:
                        # Fallback minimal payload to ensure persistence and source_index anchors
                        details_payload = {
                            'hero_summary': bill.ai_summary or bill.summary or bill.title,
                            'overview': {},
                            'positions': {},
                            'other_details': [],
                            'message_templates': {},
                            'tags': [],
                        }
                    if bill and bill.id:
                        try:
                            self.details_service.create_or_update_details(bill, bill.full_text, details_payload)
                            details_persisted = True
                        except Exception as de:
                            logger.warning(f"Persisting bill_details failed for {bill.bill_number}: {de}")
                    else:
                        logger.warning(f"Cannot create bill_details - bill has no valid ID: {bill}")

            # Refresh legacy AI fields if not processed
            if needs_ai and bill.full_text:
                ai_results = await self._run_ai_analysis(bill.full_text, {
                    'title': bill.title,
                    'summary': bill.summary or '',
                    'bill_number': bill.bill_number,
                })
                if ai_results:
                    structured_summary = ai_results.get('structured_summary', {})
                    bill.ai_summary = ai_results.get('ai_summary', '')
                    bill.tldr = ai_results.get('tldr', '')
                    bill.support_reasons = ai_results.get('support_reasons', [])
                    bill.oppose_reasons = ai_results.get('oppose_reasons', [])
                    bill.amend_reasons = ai_results.get('amend_reasons', [])
                    bill.message_templates = ai_results.get('message_templates', {})
                    bill.ai_tags = ai_results.get('tags', [])
                    bill.summary_what_does = structured_summary.get('what_does')
                    bill.summary_who_affects = structured_summary.get('who_affects')
                    bill.summary_why_matters = structured_summary.get('why_matters')
                    bill.summary_key_provisions = structured_summary.get('key_provisions')
                    bill.summary_timeline = structured_summary.get('timeline')
                    bill.summary_cost_impact = structured_summary.get('cost_impact')
                    bill.ai_processed_at = datetime.utcnow()
                    self.db.commit()

            # Values analysis
            if needs_values:
                await self._run_values_analysis(bill)

            return {
                "success": True,
                "bill_id": bill.id,
                "bill_number": bill.bill_number,
                "title": bill.title,
                "environment": environment,
                "message": f"Updated existing bill {bill.bill_number}",
                "updated": {
                    "ai_analysis": not needs_ai or bool(bill.ai_processed_at),
                    "details_persisted": details_persisted,
                    "values_analysis": not needs_values
                }
            }

        except Exception as e:
            logger.error(f"Error updating existing bill: {e}")
            return {
                "success": False,
                "error": str(e),
                "bill_number": bill.bill_number
            }

    def _map_bill_type(self, congress_type: str) -> str:
        """Map Congress.gov bill type to our model"""
        mapping = {
            'hr': 'house_bill',
            's': 'senate_bill',
            'hres': 'house_resolution',
            'sres': 'senate_resolution',
            'hjres': 'house_joint_resolution',
            'sjres': 'senate_joint_resolution',
            'hconres': 'house_concurrent_resolution',
            'sconres': 'senate_concurrent_resolution'
        }
        return mapping.get(congress_type.lower(), 'house_bill')

    def _determine_chamber(self, bill_number: str) -> str:
        """Determine chamber from bill number"""
        if not bill_number:
            return 'unknown'
        clean_number = bill_number.upper().replace('.', '')
        return 'house' if clean_number.startswith('H') else 'senate'

    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string to datetime"""
        if not date_str:
            return None
        try:
            if 'T' in date_str:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                return datetime.strptime(date_str, '%Y-%m-%d')
        except Exception:
            return None

    def _extract_sponsor_name(self, sponsors: List[Dict]) -> str:
        """Extract sponsor name"""
        if not sponsors:
            return ''
        return sponsors[0].get('fullName', '')

    def _extract_sponsor_party(self, sponsors: List[Dict]) -> str:
        """Extract sponsor party"""
        if not sponsors:
            return ''
        return sponsors[0].get('party', '')

    def _extract_sponsor_state(self, sponsors: List[Dict]) -> str:
        """Extract sponsor state"""
        if not sponsors:
            return ''
        return sponsors[0].get('state', '')


    async def _run_manual_ai_analysis(self, bill: Bill) -> Dict[str, Any]:
        """
        Run AI analysis for a bill that was initially skipped due to low importance.
        
        This method forces AI processing regardless of importance score, typically
        triggered by user request for detailed analysis.
        
        Args:
            bill: Bill instance to analyze
            
        Returns:
            Processing results
        """
        logger.info(f"Running manual AI analysis for bill {bill.bill_number}")
        
        try:
            # Get bill text - try from database first, then fetch if needed
            full_text = bill.full_text
            
            if not full_text or len(full_text.strip()) < 100:
                logger.info(f"Fetching full text for {bill.bill_number}")
                # Create minimal metadata for text fetching
                bill_metadata = {
                    "number": bill.bill_number.replace("HR", "").replace("S", ""),
                    "type": "hr" if bill.bill_number.startswith("HR") else "s",
                    "congress_session": bill.session_year or 118,
                    "title": bill.title
                }
                full_text = await self._fetch_bill_text(bill_metadata, bill.session_year or 118)
                
                if full_text:
                    bill.full_text = full_text
                    self.db.commit()
            
            if not full_text:
                return {
                    "success": False,
                    "error": "Could not retrieve bill text for analysis",
                    "bill_id": bill.id
                }
            
            # Run AI analysis using balanced approach
            bill_metadata = {
                "title": bill.title,
                "number": bill.bill_number,
                "session_year": bill.session_year,
                "bill_id": bill.id  # Add bill_id for AI usage tracking
            }
            
            cost_optimized_result = await self.ai_service.analyze_bill_balanced(
                full_text, bill_metadata, source_index=None
            )
            
            if not cost_optimized_result.get("success"):
                return {
                    "success": False,
                    "error": "AI analysis failed",
                    "bill_id": bill.id
                }
            
            # Convert results and update bill
            ai_results = self._convert_cost_optimized_to_legacy(cost_optimized_result)
            details_payload = self._convert_cost_optimized_to_details(cost_optimized_result)
            
            # Update bill with AI results
            bill.ai_summary = ai_results.get("ai_summary", "")
            bill.tldr = ai_results.get("tldr", "")
            bill.support_reasons = ai_results.get("support_reasons", [])
            bill.oppose_reasons = ai_results.get("oppose_reasons", [])
            bill.amend_reasons = ai_results.get("amend_reasons", [])
            bill.message_templates = ai_results.get("message_templates", {})
            bill.tags = ai_results.get("tags", [])
            bill.ai_processed_at = datetime.utcnow()
            self.db.commit()
            
            # Create or update bill details
            if details_payload:
                self.details_service.create_or_update_details_by_id(bill.id, full_text, details_payload)
            
            cost = cost_optimized_result.get('_metadata', {}).get('cost', 0)
            logger.info(f"Manual AI analysis completed for {bill.bill_number}: ${cost:.4f} cost")
            
            return {
                "success": True,
                "bill_id": bill.id,
                "ai_summary": ai_results.get("ai_summary", ""),
                "cost": cost,
                "processing_time": cost_optimized_result.get('_metadata', {}).get('processing_time', 0)
            }
            
        except Exception as e:
            logger.error(f"Error in manual AI analysis for {bill.bill_number}: {e}")
            self.db.rollback()
            return {
                "success": False,
                "error": str(e),
                "bill_id": bill.id
            }

    async def _re_evaluate_importance_with_evidence(self, bill: Bill, full_text: str, 
                                                  ai_results: Dict[str, Any]) -> Optional[EvidenceEnhancedScore]:
        """
        Phase 3: Re-evaluate bill importance using evidence-driven scoring
        
        Called after AI analysis is complete to provide more accurate importance
        scoring based on extracted evidence and analysis quality
        """
        
        try:
            logger.info(f"🎯 Re-evaluating importance for {bill.bill_number} with Phase 3 evidence-driven scoring")
            
            # Extract evidence spans from AI analysis results if available
            evidence_spans = None
            if ai_results and 'evidence_spans' in ai_results:
                evidence_spans = ai_results['evidence_spans']
            
            # Prepare bill metadata for enhanced scoring
            bill_metadata = {
                'title': bill.title or '',
                'summary': bill.summary or '',
                'bill_number': bill.bill_number or '',
                'bill_type': bill.bill_type.value if bill.bill_type else 'hr',
                'chamber': 'house' if bill.bill_number and bill.bill_number.upper().startswith('HR') else 'senate'
            }
            
            # Use enhanced importance scorer with evidence
            enhanced_score = await self.enhanced_importance_scorer.score_bill_with_evidence(
                bill_text=full_text,
                bill_metadata=bill_metadata,
                evidence_spans=evidence_spans
            )
            
            # Update bill priority score if significantly different
            score_difference = abs(enhanced_score.score - (bill.priority_score or 0))
            if score_difference >= 10:  # Only update if significant change
                old_score = bill.priority_score or 0
                bill.priority_score = enhanced_score.score
                self.db.commit()
                
                logger.info(f"📈 Updated importance score for {bill.bill_number}: {old_score} → {enhanced_score.score}")
                logger.info(f"   Evidence Quality: {enhanced_score.evidence_quality_score:.2f}")
                logger.info(f"   Critical Evidence: {enhanced_score.critical_evidence_count}")
                logger.info(f"   Enhanced Reason: {enhanced_score.reason}")
            else:
                logger.info(f"📊 Importance score confirmed for {bill.bill_number}: {enhanced_score.score} (evidence quality: {enhanced_score.evidence_quality_score:.2f})")
            
            return enhanced_score
            
        except Exception as e:
            logger.error(f"Enhanced importance scoring failed for {bill.bill_number}: {e}")
            return None


def get_unified_bill_processing_service(db: Session) -> UnifiedBillProcessingService:
    """Get unified bill processing service instance"""
    return UnifiedBillProcessingService(db)