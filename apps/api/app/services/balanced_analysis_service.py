"""
Balanced Analysis Service - REAL AI IMPLEMENTATION
Premium quality where users see it, efficient processing for background tasks
Target: $0.05-0.10 per bill with rich SEO-friendly detail
"""

import json
import re
import logging
import openai
import time
from typing import Dict, List, Any, Optional
from app.core.ai_routing import get_ai_router
from app.core.ai_guard import get_ai_guard
from app.services.quality_validation_service import get_quality_validator, QualityLevel
from app.services.evidence_quality_service import get_evidence_quality_service
from app.services.quality_metrics_tracking_service import get_quality_metrics_tracker
from app.services.section_templates_service import get_section_templates_service
from app.services.enhanced_citation_mining_service import get_enhanced_citation_mining_service
from app.services.optimized_section_generation_service import get_optimized_section_generation_service

logger = logging.getLogger(__name__)

class BalancedAnalysisService:
    """
    Balanced analysis service implementing the right cost/quality tradeoffs
    - Premium models (gpt-4o) for user-facing content (reasons, action content)
    - Efficient models (gpt-4o-mini) for background processing (structure, enrichment)
    - Rich detail for SEO while staying under budget
    """
    
    def __init__(self, ai_service):
        self.ai_service = ai_service
        self.router = get_ai_router()
        self.guard = get_ai_guard()
        self.quality_validator = get_quality_validator()
        self.evidence_quality_service = get_evidence_quality_service()
        self.quality_metrics_tracker = get_quality_metrics_tracker()
        self.section_templates_service = get_section_templates_service()
        self.citation_mining_service = get_enhanced_citation_mining_service()
        self.section_generation_service = get_optimized_section_generation_service()
    
    async def analyze_bill_balanced(self, bill_text: str, bill_metadata: Dict, 
                                  evidence_spans: List[Dict]) -> Dict[str, Any]:
        """
        Balanced analysis: efficient background processing + premium user content
        Expected cost: $0.05-0.10 per bill
        """
        
        bill_id = bill_metadata.get('bill_id', 'unknown')
        
        async with self.guard.bill_context(bill_id) as tracker:
            try:
                logger.info(f"🎯 Starting REAL balanced analysis for {bill_metadata.get('title', 'Unknown')}")

                # PHASE 2: Validate and filter evidence spans for quality
                evidence_spans = self._ensure_evidence_ids(evidence_spans)
                validated_evidence, evidence_quality = self.evidence_quality_service.validate_evidence_spans(evidence_spans)
                
                if len(validated_evidence) < len(evidence_spans):
                    logger.info(f"📊 Evidence quality filter: {len(validated_evidence)}/{len(evidence_spans)} spans validated (avg quality: {evidence_quality['average_quality_score']:.2f})")
                
                # Use validated evidence for analysis
                evidence_spans = validated_evidence

                # Step 1: PHASE 3.2 - Hierarchical analysis with section templates
                structure_analysis = self.section_templates_service.analyze_bill_structure(bill_text, evidence_spans)
                logger.info(f"📋 Structure analysis: {structure_analysis['estimated_sections']} sections, {structure_analysis['hr5118_coverage']:.1%} HR5-118 coverage")
                
                # Step 1.5: PHASE 3.3 - Enhanced citation mining for comprehensive evidence grounding
                citation_analysis = self.citation_mining_service.mine_comprehensive_citations(bill_text, evidence_spans, bill_metadata)
                logger.info(f"🔍 Citation analysis: {citation_analysis['citation_stats']['total_citations']} enhanced citations, {citation_analysis['citation_stats']['high_importance']} high-importance")
                
                # Use enhanced evidence spans with citation context
                enhanced_evidence_spans = citation_analysis['enhanced_evidence_spans']
                
                # Step 1.75: PHASE 3.4 - Optimized section generation strategy
                generation_optimization = self.section_generation_service.optimize_section_generation(
                    bill_text, bill_metadata, structure_analysis, citation_analysis, target_sections=35
                )
                logger.info(f"🎯 Generation optimization: {generation_optimization['target_achievement']['optimized_sections']} optimized sections, {generation_optimization['target_achievement']['achievement_rate']:.1%} target achievement")
                
                # Step 2: Skeleton pass with optimized section strategy (efficient background processing)
                skeleton_result = await self._skeleton_pass_optimized(bill_text, bill_metadata, enhanced_evidence_spans, generation_optimization, bill_id)
                
                if not skeleton_result.get('success'):
                    return skeleton_result
                
                skeleton_analysis = skeleton_result['analysis']
                
                # Step 3: Premium content generation (user-facing)
                user_content = await self._generate_premium_content(
                    bill_text, bill_metadata, skeleton_analysis, enhanced_evidence_spans, bill_id
                )
                
                # Step 4: Combine all analysis
                final_analysis = self._combine_analysis(skeleton_analysis, [], user_content, enhanced_evidence_spans)

                # Step 5: PHASE 2 - Quality validation against HR5-118 standards
                quality_metrics = self.quality_validator.validate_analysis_quality(final_analysis, bill_metadata)
                
                # PHASE 2.3: Section count enhancement loop - retry if < 25 sections (WITH SAFEGUARDS)
                sections_generated = len(final_analysis.get('complete_analysis', []))
                enhancement_attempted = False  # Prevent infinite loops
                
                if sections_generated < 25 and not enhancement_attempted:
                    logger.warning(f"⚠️ Section count below target: {sections_generated}/25+ sections, triggering ONE enhancement attempt")
                    enhancement_attempted = True
                    
                    # Check remaining budget before expensive retry
                    bill_status = self.guard.get_bill_status(bill_id)
                    if bill_status['budget_remaining'] < 0.05:
                        logger.warning(f"⚠️ Insufficient budget for enhancement: ${bill_status['budget_remaining']:.4f}")
                    else:
                        try:
                            # Single retry attempt with optimization-enhanced prompt
                            enhanced_target = max(30, generation_optimization['target_achievement']['optimized_sections'])
                            enhanced_skeleton_result = await self._enhanced_skeleton_pass_optimized(bill_text, bill_metadata, enhanced_evidence_spans, generation_optimization, bill_id, target_sections=enhanced_target)
                            
                            if enhanced_skeleton_result.get('success'):
                                enhanced_skeleton = enhanced_skeleton_result['analysis']
                                enhanced_sections = len(enhanced_skeleton.get('complete_analysis', []))
                                logger.info(f"📈 Enhanced skeleton generated: {enhanced_sections} sections")
                                
                                if enhanced_sections > sections_generated:
                                    # Use enhanced skeleton if it's better
                                    skeleton_analysis = enhanced_skeleton
                                    final_analysis = self._combine_analysis(skeleton_analysis, [], user_content, enhanced_evidence_spans)
                                    logger.info(f"✅ Used enhanced skeleton: {enhanced_sections} sections")
                                else:
                                    logger.info(f"📊 Enhanced skeleton didn't improve: {enhanced_sections} vs {sections_generated}")
                            else:
                                logger.warning(f"⚠️ Enhanced skeleton pass failed: {enhanced_skeleton_result.get('error', 'Unknown error')}")
                        except Exception as e:
                            logger.error(f"❌ Enhancement attempt failed: {e}")
                            # Continue with original analysis
                else:
                    if sections_generated < 25:
                        logger.info(f"⚠️ {sections_generated} sections generated (below target), but enhancement already attempted - proceeding")
                
                # Step 6: Quality-based improvements if needed
                if quality_metrics.quality_level in [QualityLevel.NEEDS_IMPROVEMENT, QualityLevel.POOR]:
                    logger.warning(f"⚠️ Quality below standards ({quality_metrics.overall_score:.2f}), attempting improvements")
                    final_analysis = await self._improve_analysis_quality(
                        final_analysis, quality_metrics, bill_text, bill_metadata, enhanced_evidence_spans, bill_id
                    )
                    
                    # Re-validate after improvements
                    quality_metrics = self.quality_validator.validate_analysis_quality(final_analysis, bill_metadata)
                    logger.info(f"📈 Post-improvement quality: {quality_metrics.quality_level.value} ({quality_metrics.overall_score:.2f})")

                # Step 7: Create details_payload for bill_details with evidence store
                evidence_store = {span['id']: span for span in enhanced_evidence_spans}
                details_payload = self._create_details_payload(final_analysis, bill_metadata, evidence_store)

                # Get final cost breakdown
                bill_status = self.guard.get_bill_status(bill_id)

                # Step 8: PHASE 2 - Record quality metrics for tracking
                cost_breakdown = {
                    'total_cost': bill_status['spent'],
                    'skeleton_cost': skeleton_result.get('cost', 0),
                    'premium_content_cost': user_content.get('cost', 0),
                    'budget_remaining': bill_status['budget_remaining'],
                    'budget_exhausted': bill_status['spent'] >= 0.25  # $0.25 cap
                }
                
                quality_metrics_dict = {
                    'overall_score': quality_metrics.overall_score,
                    'quality_level': quality_metrics.quality_level.value,
                    'specificity_score': quality_metrics.specificity_score,
                    'evidence_grounding_score': quality_metrics.evidence_grounding_score,
                    'comprehensiveness_score': quality_metrics.comprehensiveness_score,
                    'clarity_score': quality_metrics.clarity_score,
                    'actionability_score': quality_metrics.actionability_score,
                    'issues': quality_metrics.issues,
                    'recommendations': quality_metrics.recommendations
                }
                
                # Record quality metrics for tracking and trend analysis
                self.quality_metrics_tracker.record_quality_metrics(
                    bill_id=bill_id,
                    bill_title=bill_metadata.get('title', 'Unknown'),
                    quality_metrics=quality_metrics_dict,
                    evidence_quality=evidence_quality,
                    cost_breakdown=cost_breakdown
                )

                logger.info(f"✅ REAL Balanced analysis completed: ${bill_status['spent']:.4f}, Quality: {quality_metrics.quality_level.value}")

                return {
                    'success': True,
                    'analysis': final_analysis,
                    'details_payload': details_payload,
                    'quality_metrics': quality_metrics_dict,
                    'evidence_quality': evidence_quality,
                    'cost_breakdown': cost_breakdown,
                    'phase3_enhancements': {
                        'structure_analysis': structure_analysis,
                        'citation_analysis': citation_analysis['citation_stats'],
                        'evidence_clusters': len(citation_analysis['evidence_clusters']),
                        'generation_optimization': generation_optimization['target_achievement'],
                        'hierarchical_structure': skeleton_result.get('structure_info', {}),
                        'optimization_strategy': generation_optimization['selected_strategy'].strategy_name,
                        'total_candidate_sections': generation_optimization.get('expanded_candidates', 0),
                        'final_optimized_sections': generation_optimization['target_achievement']['optimized_sections']
                    }
                }
                
            except Exception as e:
                logger.error(f"Balanced analysis failed: {e}")
                return {
                    'success': False,
                    'error': str(e),
                    'cost_breakdown': {
                        'total_cost': self.guard.get_bill_status(bill_id)['spent'],
                        'budget_remaining': self.guard.get_bill_status(bill_id)['budget_remaining']
                    }
                }
    
    async def _skeleton_pass_optimized(self, bill_text: str, bill_metadata: Dict, 
                                      evidence_spans: List[Dict], generation_optimization: Dict,
                                      bill_id: str) -> Dict[str, Any]:
        """
        PHASE 3.4: Optimized skeleton pass using advanced section generation strategy
        """
        
        # Use optimized prompt from generation service
        prompt = generation_optimization['generation_prompts']['optimized_prompt']
        target_sections = generation_optimization['target_achievement']['optimized_sections']
        
        # Estimate tokens
        input_tokens = len(prompt.split()) * 1.3
        
        # Execute with guard
        result = await self.guard.guarded_call(
            operation_type="skeleton_pass",
            ai_function=self._call_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=prompt,
            schema=self._get_optimized_skeleton_schema(target_sections)
        )
        
        if not result.success:
            logger.error(f"Optimized skeleton pass failed: {result.error}")
            return {'success': False, 'error': result.error, 'cost': result.cost}
        
        try:
            analysis = json.loads(result.content)
            
            # PHASE 3.4: Apply optimization-guided post-processing
            optimized_analysis = self._apply_optimization_post_processing(analysis, generation_optimization)
            
            return {
                'success': True,
                'analysis': optimized_analysis,
                'cost': result.cost,
                'optimization_info': generation_optimization['target_achievement']
            }
        except json.JSONDecodeError as e:
            logger.error(f"Optimized skeleton pass JSON parse failed: {e}")
            return {'success': False, 'error': f"JSON parse error: {e}", 'cost': result.cost}

    async def _skeleton_pass_with_templates(self, bill_text: str, bill_metadata: Dict, 
                                           evidence_spans: List[Dict], structure_analysis: Dict,
                                           bill_id: str) -> Dict[str, Any]:
        """
        PHASE 3.2: Skeleton analysis with section template guidance
        Creates hierarchical structure with template-based organization
        """
        
        # Create template-guided skeleton prompt
        prompt = self.section_templates_service.generate_enhanced_prompt_with_templates(
            bill_text, bill_metadata, evidence_spans, structure_analysis
        )
        
        # Estimate tokens
        input_tokens = len(prompt.split()) * 1.3  # Rough estimate
        
        # Execute with guard
        result = await self.guard.guarded_call(
            operation_type="skeleton_pass",
            ai_function=self._call_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=prompt,
            schema=self._get_template_skeleton_schema(structure_analysis['estimated_sections'])
        )
        
        if not result.success:
            logger.error(f"Template skeleton pass failed: {result.error}")
            return {'success': False, 'error': result.error, 'cost': result.cost}
        
        try:
            analysis = json.loads(result.content)
            
            # PHASE 3.2: Apply hierarchical organization to the analysis
            hierarchical_analysis = self._apply_hierarchical_organization(analysis, structure_analysis)
            
            return {
                'success': True,
                'analysis': hierarchical_analysis,
                'cost': result.cost,
                'structure_info': structure_analysis
            }
        except json.JSONDecodeError as e:
            logger.error(f"Template skeleton pass JSON parse failed: {e}")
            return {'success': False, 'error': f"JSON parse error: {e}", 'cost': result.cost}

    async def _skeleton_pass(self, bill_text: str, bill_metadata: Dict, 
                           evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """
        Pass A: Skeleton analysis using efficient model (fallback method)
        Creates structure with concise, grounded content
        """
        
        # Create skeleton prompt
        prompt = self._build_skeleton_prompt(bill_text, bill_metadata, evidence_spans)
        
        # Estimate tokens
        input_tokens = len(prompt.split()) * 1.3  # Rough estimate
        
        # Execute with guard
        result = await self.guard.guarded_call(
            operation_type="skeleton_pass",
            ai_function=self._call_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=prompt,
            schema=self._get_skeleton_schema()
        )
        
        if not result.success:
            logger.error(f"Skeleton pass failed: {result.error}")
            return {'success': False, 'error': result.error, 'cost': result.cost}
        
        try:
            analysis = json.loads(result.content)
            return {
                'success': True,
                'analysis': analysis,
                'cost': result.cost
            }
        except json.JSONDecodeError as e:
            logger.error(f"Skeleton pass JSON parse failed: {e}")
            return {'success': False, 'error': f"JSON parse error: {e}", 'cost': result.cost}
    
    async def _enhanced_skeleton_pass_with_templates(self, bill_text: str, bill_metadata: Dict, 
                                                   evidence_spans: List[Dict], structure_analysis: Dict,
                                                   bill_id: str, target_sections: int = 30) -> Dict[str, Any]:
        """
        PHASE 3.2: Enhanced skeleton pass with template-guided hierarchical analysis
        """
        
        # Use template service to generate enhanced structured prompt
        enhanced_structure = {
            **structure_analysis,
            'estimated_sections': max(target_sections, structure_analysis['estimated_sections'])
        }
        
        enhanced_prompt = self.section_templates_service.generate_enhanced_prompt_with_templates(
            bill_text, bill_metadata, evidence_spans, enhanced_structure
        )
        
        # Add additional enhancement instructions
        enhanced_prompt += f"""

PHASE 3.2 HIERARCHICAL ENHANCEMENT:
- Generate nested sections following template hierarchy
- Each main section should have 2-5 subsections where appropriate
- Use legal document structure: SEC. → (a), (b), (c) → (1), (2), (3)
- Ensure comprehensive coverage of all bill provisions
- Target {target_sections}+ total sections with hierarchical organization
"""
        
        # Estimate tokens
        input_tokens = len(enhanced_prompt.split()) * 1.3
        
        # Execute with guard - use higher temperature for creativity
        result = await self.guard.guarded_call(
            operation_type="enhanced_skeleton_pass",
            ai_function=self._call_enhanced_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=enhanced_prompt,
            schema=self._get_enhanced_template_skeleton_schema(target_sections),
            target_sections=target_sections
        )
        
        if not result.success:
            logger.error(f"Enhanced template skeleton pass failed: {result.error}")
            return {'success': False, 'error': result.error, 'cost': result.cost}
        
        try:
            analysis = json.loads(result.content)
            
            # PHASE 3.2: Apply hierarchical organization with enhanced structure
            hierarchical_analysis = self._apply_hierarchical_organization(analysis, enhanced_structure)
            
            return {
                'success': True,
                'analysis': hierarchical_analysis,
                'cost': result.cost,
                'structure_info': enhanced_structure
            }
        except json.JSONDecodeError as e:
            logger.error(f"Enhanced template skeleton pass JSON parse failed: {e}")
            return {'success': False, 'error': f"JSON parse error: {e}", 'cost': result.cost}

    async def _enhanced_skeleton_pass_optimized(self, bill_text: str, bill_metadata: Dict, 
                                              evidence_spans: List[Dict], generation_optimization: Dict,
                                              bill_id: str, target_sections: int = 35) -> Dict[str, Any]:
        """
        PHASE 3.4: Enhanced skeleton pass with optimization-guided strategy
        """
        
        # Build enhanced optimized prompt
        base_prompt = generation_optimization['generation_prompts']['optimized_prompt']
        
        # Add enhancement instructions
        enhanced_prompt = base_prompt + f"""

PHASE 3.4 ENHANCEMENT INSTRUCTIONS:
- CRITICAL: Generate EXACTLY {target_sections} detailed sections minimum
- Use SURGICAL PRECISION: Each section must be legally precise and evidence-grounded
- EXPAND beyond the optimization suggestions if high-quality evidence supports it
- Focus on HR5-118 GOLD STANDARD: specific amounts, deadlines, penalties, entities
- Quality over quantity: Better to have {target_sections} excellent sections than rushed analysis

ENHANCED QUALITY REQUIREMENTS:
- Each section minimum 120 words of substantive legal content
- Minimum 2 evidence citations per section 
- Specific statutory references where applicable
- Named entities (not "relevant agencies")
- Exact dollar amounts, deadlines, and penalties
- Actionable language describing specific requirements

Generate comprehensive analysis targeting {target_sections}+ sections with enhanced legal precision."""

        # Estimate tokens
        input_tokens = len(enhanced_prompt.split()) * 1.3
        
        # Execute with enhanced settings
        result = await self.guard.guarded_call(
            operation_type="enhanced_skeleton_pass",
            ai_function=self._call_enhanced_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=enhanced_prompt,
            schema=self._get_enhanced_optimized_skeleton_schema(target_sections),
            target_sections=target_sections
        )
        
        if not result.success:
            logger.error(f"Enhanced optimized skeleton pass failed: {result.error}")
            return {'success': False, 'error': result.error, 'cost': result.cost}
        
        try:
            analysis = json.loads(result.content)
            
            # Apply enhanced optimization post-processing
            enhanced_optimized_analysis = self._apply_optimization_post_processing(analysis, generation_optimization)
            
            return {
                'success': True,
                'analysis': enhanced_optimized_analysis,
                'cost': result.cost,
                'optimization_info': generation_optimization['target_achievement']
            }
        except json.JSONDecodeError as e:
            logger.error(f"Enhanced optimized skeleton pass JSON parse failed: {e}")
            return {'success': False, 'error': f"JSON parse error: {e}", 'cost': result.cost}

    async def _enhanced_skeleton_pass(self, bill_text: str, bill_metadata: Dict, 
                                    evidence_spans: List[Dict], bill_id: str, 
                                    target_sections: int = 35) -> Dict[str, Any]:
        """
        PHASE 2.3: Enhanced skeleton pass with stronger prompt for more sections (fallback method)
        """
        
        # Create enhanced prompt specifically targeting more sections
        enhanced_prompt = self._build_enhanced_skeleton_prompt(bill_text, bill_metadata, evidence_spans, target_sections)
        
        # Estimate tokens
        input_tokens = len(enhanced_prompt.split()) * 1.3
        
        # Execute with guard - use higher temperature for creativity
        result = await self.guard.guarded_call(
            operation_type="enhanced_skeleton_pass",
            ai_function=self._call_enhanced_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=enhanced_prompt,
            schema=self._get_enhanced_skeleton_schema(target_sections),
            target_sections=target_sections
        )
        
        if not result.success:
            logger.error(f"Enhanced skeleton pass failed: {result.error}")
            return {'success': False, 'error': result.error, 'cost': result.cost}
        
        try:
            analysis = json.loads(result.content)
            return {
                'success': True,
                'analysis': analysis,
                'cost': result.cost
            }
        except json.JSONDecodeError as e:
            logger.error(f"Enhanced skeleton pass JSON parse failed: {e}")
            return {'success': False, 'error': f"JSON parse error: {e}", 'cost': result.cost}
    
    async def _generate_premium_content(self, bill_text: str, bill_metadata: Dict,
                                      skeleton_analysis: Dict, evidence_spans: List[Dict],
                                      bill_id: str) -> Dict[str, Any]:
        """
        Generate premium user-facing content using high-quality model
        This is where we spend money for quality that users see
        """
        
        # Extract key information for reasons
        key_sections = skeleton_analysis.get('complete_analysis', [])[:3]  # Top 3 sections
        
        # Build premium content prompt
        prompt = self._build_premium_content_prompt(
            bill_metadata, key_sections, evidence_spans
        )
        
        input_tokens = len(prompt.split()) * 1.3
        
        # Use premium model for user-facing content
        result = await self.guard.guarded_call(
            operation_type="reason_generation",
            ai_function=self._call_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=prompt,
            schema=self._get_premium_content_schema()
        )
        
        if not result.success:
            logger.warning(f"Premium content generation failed: {result.error}")
            return {'cost': result.cost}
        
        try:
            content = json.loads(result.content)
            content['cost'] = result.cost
            return content
        except json.JSONDecodeError as e:
            logger.error(f"Premium content JSON parse failed: {e}")
            return {'cost': result.cost}
    
    def _build_skeleton_prompt(self, bill_text: str, bill_metadata: Dict, 
                             evidence_spans: List[Dict]) -> str:
        """Build enhanced skeleton analysis prompt - PHASE 2 UPGRADE"""
        
        # Enhanced evidence presentation with quality metrics
        evidence_text = "\n".join([
            f"ID: {span['id']}\n"
            f"Heading: {span['heading']}\n"
            f"Quote: {span['quote'][:150]}...\n"
            f"Quality: {span.get('quality_metrics', {}).get('quality_level', 'unknown')}\n"
            f"Grounding Value: {span.get('quality_metrics', {}).get('grounding_value', 0):.2f}"
            for span in evidence_spans[:100]  # PHASE 2.2: Increased to 100 evidence spans for comprehensive analysis
        ])
        
        # Use full bill text for comprehensive HR5-118 quality analysis
        bill_text_excerpt = str(bill_text) if bill_text else "No bill text available"
        
        # HR5-118 standard requirements
        hr5118_requirements = """
CRITICAL: This analysis must meet HR5-118 GOLD STANDARD requirements:
- SPECIFIC monetary amounts (exact dollars, not "funding is provided")  
- PRECISE deadlines (exact dates/timeframes, not "timely implementation")
- CONCRETE enforcement mechanisms (specific penalties, not "enforcement measures")
- CLEAR affected parties (named entities, not "various stakeholders")
- EVIDENCE-GROUNDED claims (every statement must cite evidence ID)
- ACTIONABLE language (what specifically happens, not generic descriptions)
        """

        return f"""
{hr5118_requirements}

Analyze this bill with MAXIMUM SPECIFICITY and EVIDENCE GROUNDING.

🚨 CRITICAL EVIDENCE INTEGRATION REQUIREMENTS:
- EVERY section must populate ev_ids array with relevant evidence IDs from the list below
- NEVER leave ev_ids empty - always include at least 1-3 evidence IDs per section
- Each claim/statement MUST be supported by evidence ID references
- Use evidence IDs like: {', '.join([span['id'] for span in evidence_spans[:10]])}

Bill: {bill_metadata.get('title', 'Unknown')}

AVAILABLE EVIDENCE (YOU MUST USE THESE IDs):
{evidence_text}

Bill Text (excerpt):
{bill_text_excerpt}

CRITICAL TASK: Create a comprehensive complete_analysis with EXACTLY 35-44 detailed sections (HR5-118 SURGICAL PRECISION standard).

🎯 MANDATORY SECTION REQUIREMENTS:
You MUST generate AT LEAST 35 sections in the complete_analysis array. Generate one section for:
- Each SEC. in the bill (e.g., "SEC. 2. YOUTH OFFENDERS")
- Each major subsection (e.g., "(a) Limitation of Youth Offender Status")
- Each significant paragraph or amendment
- Each enforcement mechanism
- Each funding provision
- Each timeline requirement

ENHANCED ANALYSIS REQUIREMENTS:
- Analyze each SEC. X section separately with legal precision
- Break down major paragraphs (1), (2), (3) into separate sections when substantive
- Include exact statutory citations: "Section 16-2333, District of Columbia Official Code"  
- Use precise legal language in titles
- Identify specific amendments and their effects

IMPROVED TITLE FORMATS (more HR5-118 style):
- "SEC. 2: YOUTH OFFENDERS - Age Limitation Provisions"
- "Paragraph (1): Limitation of Youth Offender Status to Age 18"
- "Amendment of Section 16-2333, District of Columbia Official Code"  
- "Subsection (b): Website Establishment Requirements"
Each section MUST include:
- title: Specific, descriptive title (not generic)
- importance: primary/secondary/technical (based on monetary impact, enforcement, or mandate strength)
- detailed_summary: SPECIFIC details with exact amounts, dates, requirements
- key_actions: CONCRETE actions (not "review" or "implement" - be specific)
- affected_parties: NAMED entities (not "government agencies" - be specific)
- ev_ids: Evidence IDs supporting EVERY major claim (NEVER empty - use 1-3 IDs per section)

🔗 EVIDENCE LINKING REQUIREMENTS:
- EVERY section must have populated ev_ids array with 1-3 evidence IDs
- Match evidence IDs to section content based on topic/keywords
- For funding sections: use evidence IDs that contain money amounts
- For timeline sections: use evidence IDs that contain deadlines
- For enforcement: use evidence IDs that contain penalties/requirements
- NEVER use empty ev_ids arrays - this is a critical failure

ENHANCED LEGAL ANALYSIS REQUIREMENTS:
1. **Section-by-section analysis** - Each SEC. X gets detailed coverage
2. **Major paragraph breakdown** - Substantive paragraphs (1), (2), (3) analyzed separately
3. **Statutory citations** - Include specific law references and U.S.C. sections  
4. **Amendment identification** - Clearly identify what existing laws are modified
5. **Legal language precision** - Use exact legal terminology from the bill

FOCUS ON QUALITY OVER QUANTITY:
- Better to have 25-30 high-quality, detailed sections than rushed analysis
- Each section should have substantive content with specific legal details
- Include real statutory references and amendment language
- Use precise legal terminology from the actual bill text

AVOID GENERIC LANGUAGE: No "comprehensive provisions", "various stakeholders", "appropriate measures"
USE SPECIFIC LANGUAGE: "$50 million to Department X for purpose Y", "90-day implementation deadline", "civil penalty up to $100,000"

EXAMPLE STRUCTURE (HR5-118 STANDARD - 44+ sections):
You MUST generate similar granular detail for your complete_analysis array:

1. SEC. 1: SHORT TITLE - Federal Data Act of 2023
2. SEC. 2: DEFINITIONS - Key Terms and Classifications
3. SEC. 3: ESTABLISHMENT OF FRAMEWORK
4. Subsection 3(a): Agency Requirements
5. Paragraph 3(a)(1): Initial Assessment Timeline
6. Paragraph 3(a)(2): Compliance Documentation
7. Paragraph 3(a)(3): Exception Procedures
8. Subsection 3(b): Standards Development
9. Paragraph 3(b)(1): Technical Specifications
10. Paragraph 3(b)(2): Interoperability Requirements
11. SEC. 4: IMPLEMENTATION TIMELINE
12. Subsection 4(a): Phase 1 - Planning (90 days)
13. Subsection 4(b): Phase 2 - Development (180 days)
14. Subsection 4(c): Phase 3 - Deployment (365 days)
15. SEC. 5: ENFORCEMENT MECHANISMS
16. Subsection 5(a): Civil Penalties
17. Paragraph 5(a)(1): First Violation - $50,000
18. Paragraph 5(a)(2): Repeat Violations - $100,000
19. Subsection 5(b): Administrative Actions
20. SEC. 6: FUNDING PROVISIONS
21. Subsection 6(a): Authorization of Appropriations
22. Subsection 6(b): Distribution Formula
23. SEC. 7: AMENDMENTS TO EXISTING LAW
24. Amendment to Section 208 of Title 44, United States Code
25. Amendment to Section 3502 of Title 44, United States Code
26. Amendment to Section 3506 of Title 44, United States Code
27. SEC. 8: REPORTING REQUIREMENTS
28. Subsection 8(a): Annual Reports to Congress
29. Subsection 8(b): Public Disclosure Requirements
30. Subsection 8(c): Performance Metrics
31. SEC. 9: PRIVACY PROTECTIONS
32. Subsection 9(a): Data Security Standards
33. Subsection 9(b): Individual Rights
34. SEC. 10: OVERSIGHT AND GOVERNANCE
35. Subsection 10(a): Federal Advisory Committee
36. Subsection 10(b): Inspector General Reviews
37. SEC. 11: TRANSITION PROVISIONS
38. Subsection 11(a): Existing System Migration
39. Subsection 11(b): Legacy Data Handling
40. SEC. 12: TECHNICAL CORRECTIONS
41. Correction to Section 1001 of Previous Act
42. Correction to Section 2005 of Previous Act
43. SEC. 13: EFFECTIVE DATE - 180 days after enactment
44. SEC. 14: SUNSET PROVISION - Review after 5 years

Generate AT LEAST 25 sections with this level of granular, paragraph-by-paragraph precision!

🚨 CRITICAL EVIDENCE INTEGRATION REQUIREMENTS:
- EVERY section must populate ev_ids array with relevant evidence IDs from the evidence context
- NEVER leave ev_ids empty - always include at least 1-3 evidence IDs per section
- Match evidence IDs to section content based on topic/keywords
- Use evidence IDs like: {', '.join([span['id'] for span in evidence_spans[:10]])}

AVAILABLE EVIDENCE CONTEXT:
{evidence_context}
"""
    
    def _build_premium_content_prompt(self, bill_metadata: Dict, 
                                    key_sections: List[Dict], 
                                    evidence_spans: List[Dict]) -> str:
        """Build enhanced premium user-facing content prompt - PHASE 2 UPGRADE"""
        
        # Enhanced sections with quality focus
        sections_text = "\n".join([
            f"Section: {section.get('title', 'Unknown')}\n"
            f"Summary: {section.get('detailed_summary', '')}\n"
            f"Key Actions: {', '.join(section.get('key_actions', []))}\n"
            f"Affected Parties: {', '.join(section.get('affected_parties', []))}\n"
            f"Evidence IDs: {', '.join(section.get('ev_ids', []))}"
            for section in key_sections
        ])
        
        # High-quality evidence summary for citizen context
        citizen_relevant_evidence = "\n".join([
            f"Evidence {span['id']}: {span.get('quote', '')[:100]}..."
            for span in evidence_spans[:6] 
            if span.get('quality_metrics', {}).get('grounding_value', 0) > 0.6
        ])
        
        return f"""
GENERATE HIGH-QUALITY CITIZEN-FACING POSITIONS meeting HR5-118 GOLD STANDARD.

REQUIREMENTS:
- Use specific impacts, not generic statements
- Reference exact dollar amounts, deadlines, penalties from evidence
- Explain concrete consequences for different citizen groups
- Ground every position in specific evidence (include ev_ids)
- Write at 8th grade level but with substantive detail
- Avoid political bias - focus on factual impacts

Bill: {bill_metadata.get('title', 'Unknown')}

Key Sections with Specific Details:
{sections_text}

High-Quality Evidence for Citizen Impact:
{citizen_relevant_evidence}

Generate SPECIFIC, EVIDENCE-BASED positions:

1. SUPPORT REASONS (3-4 reasons):
   - Focus on concrete benefits with specific amounts/timelines
   - Identify which citizen groups benefit and how
   - Use evidence IDs to ground each claim
   - Example format: "This bill provides $X million for [specific purpose], helping [specific group] by [specific benefit] within [timeframe]"

2. OPPOSE REASONS (3-4 reasons):
   - Focus on concrete costs, burdens, or restrictions
   - Identify which groups are negatively affected and how
   - Use evidence IDs to ground each claim  
   - Example format: "This bill imposes [specific requirement] on [specific group], with penalties up to $X for non-compliance"

3. AMENDMENT SUGGESTIONS (2-3 suggestions):
   - Propose specific improvements with clear rationale
   - Reference evidence showing gaps or problems
   - Suggest concrete modifications to provisions
   - Example format: "Modify Section X to [specific change] because [evidence-based rationale]"

AVOID: "comprehensive provisions", "various stakeholders", "appropriate measures", "significant impact"
USE: Specific amounts, named entities, exact timeframes, concrete requirements, measurable outcomes
"""
    
    async def _call_openai_json(self, prompt: str, schema: Dict, **kwargs) -> Dict[str, Any]:
        """Call OpenAI with JSON mode - REAL AI ANALYSIS"""
        
        # Get the AI service client
        if not hasattr(self.ai_service, 'client') or not self.ai_service.client:
            raise RuntimeError("OpenAI client not available")
        
        try:
            start_time = time.time()
            
            # PHASE 2: Enhanced system prompt for HR5-118 standards
            enhanced_system_prompt = """You are an expert legislative analyst trained to HR5-118 GOLD STANDARD requirements.

CRITICAL ANALYSIS STANDARDS:
- Use SPECIFIC monetary amounts (exact dollars, not "funding provided")
- Include PRECISE deadlines (exact dates/timeframes, not "implementation required")  
- Identify CONCRETE enforcement mechanisms (specific penalties, fines, sanctions)
- Name SPECIFIC affected parties (actual entities, not "various stakeholders")
- Ground EVERY claim in evidence IDs (no unsupported statements)
- Use ACTIONABLE language (what specifically happens, not generic descriptions)

QUALITY REQUIREMENTS:
- Eliminate generic phrases: "comprehensive provisions", "various stakeholders", "appropriate measures"
- Use specific language: "$50 million to EPA for water monitoring", "90-day implementation deadline", "civil penalty up to $100,000"
- Every section must cite evidence IDs for major claims
- Focus on money, mandates, enforcement, deadlines, and specific impacts

Analyze bills thoroughly and provide detailed, accurate analysis in JSON format. Always ground your analysis in the actual bill text and evidence provided."""

            # Make real OpenAI API call with enhanced system prompt
            response = await self.ai_service.client.chat.completions.create(
                model="gpt-4o",  # PHASE 2.1: Upgraded to gpt-4o for better section generation
                messages=[
                    {"role": "system", "content": enhanced_system_prompt},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4500,  # Increased for more detailed analysis
                temperature=0.9,  # INCREASED for more creative section generation
                response_format={"type": "json_object"}
            )
            
            response_time_ms = (time.time() - start_time) * 1000
            
            # Parse the JSON response
            content = response.choices[0].message.content
            parsed_content = json.loads(content)
            
            # Calculate cost (gpt-4o pricing - PHASE 2.1)
            prompt_cost = response.usage.prompt_tokens * 0.0025 / 1000  # $2.50 per 1K tokens
            completion_cost = response.usage.completion_tokens * 0.010 / 1000  # $10.00 per 1K tokens
            total_cost = prompt_cost + completion_cost
            
            logger.info(f"✅ Real OpenAI call completed: {response.usage.total_tokens} tokens, ${total_cost:.4f}")
            
            return {
                "success": True,
                "content": content,
                "parsed_content": parsed_content,
                "tokens_used": response.usage.total_tokens,
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "cost": total_cost,
                "prompt_cost": prompt_cost,
                "completion_cost": completion_cost,
                "response_time_ms": response_time_ms
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse OpenAI JSON response: {e}")
            return {
                "success": False,
                "error": f"JSON parse error: {e}",
                "content": response.choices[0].message.content if 'response' in locals() else None
            }
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _get_skeleton_schema(self) -> Dict:
        """JSON schema for skeleton analysis"""
        return {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "minItems": 25,  # FORCE minimum 25 sections
                    "maxItems": 50,  # Cap at 50 to prevent token overflow
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "detailed_summary": {"type": "string"},
                            "key_actions": {"type": "array", "items": {"type": "string"}},
                            "affected_parties": {"type": "array", "items": {"type": "string"}},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                }
            }
        }

    def _get_premium_content_schema(self) -> Dict:
        """JSON schema for premium content"""
        return {
            "type": "object",
            "properties": {
                "support_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "reason": {"type": "string"},
                            "explanation": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                },
                "oppose_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "reason": {"type": "string"},
                            "explanation": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                },
                "amend_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "suggestion": {"type": "string"},
                            "rationale": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                }
            }
        }

    def _combine_analysis(self, skeleton: Dict, enriched_sections: List[Dict],
                         user_content: Dict, evidence_spans: List[Dict]) -> Dict:
        """Combine all analysis components"""

        # Build evidence store for citation resolution
        evidence_store = {span['id']: span for span in evidence_spans}

        # CRITICAL FIX: Normalize AI response structure and populate missing evidence IDs
        normalized_skeleton = self._normalize_analysis_structure(skeleton, evidence_spans)

        # Resolve evidence IDs to full citations
        final_analysis = self._resolve_evidence_citations(normalized_skeleton, evidence_store)

        # Add premium user content
        final_analysis['user_content'] = user_content

        # Add free enrichments (deterministic extraction)
        final_analysis['additional_details'] = self._extract_free_enrichments(evidence_spans)

        return final_analysis

    def _normalize_analysis_structure(self, skeleton: Dict, evidence_spans: List[Dict]) -> Dict:
        """
        CRITICAL FIX: Normalize AI response structure and populate missing evidence IDs
        The AI sometimes generates different field names or empty ev_ids arrays
        """
        logger.error("🔧 Normalizing analysis structure and fixing evidence integration")  # Use error level to ensure visibility
        
        complete_analysis = skeleton.get('complete_analysis', [])
        evidence_ids = [span['id'] for span in evidence_spans[:20]]  # First 20 evidence IDs for assignment
        
        normalized_sections = []
        
        for i, section in enumerate(complete_analysis):
            normalized_section = {}
            
            # Fix field name mismatches
            normalized_section['title'] = (
                section.get('title') or 
                section.get('section_title') or 
                section.get('name') or 
                f"Section {i+1} Analysis"
            )
            
            # Ensure other required fields exist
            normalized_section['importance'] = section.get('importance', 'secondary')
            normalized_section['detailed_summary'] = (
                section.get('detailed_summary') or 
                section.get('content') or 
                section.get('summary') or
                "Analysis of bill provisions"
            )
            
            # Fix key_actions field
            key_actions = section.get('key_actions', [])
            if isinstance(key_actions, str):
                key_actions = [key_actions]
            elif not key_actions:
                # Generate basic actions from content
                content = normalized_section['detailed_summary']
                if 'enforcement' in content.lower():
                    key_actions = ['Implement enforcement measures']
                elif 'funding' in content.lower() or '$' in content:
                    key_actions = ['Allocate specified funding']
                elif 'deadline' in content.lower() or 'days' in content:
                    key_actions = ['Meet implementation deadline']
                else:
                    key_actions = ['Review bill provisions']
            normalized_section['key_actions'] = key_actions[:3]  # Limit to 3
            
            # Fix affected_parties field  
            affected_parties = section.get('affected_parties', [])
            if isinstance(affected_parties, str):
                affected_parties = [affected_parties]
            elif not affected_parties:
                # Generate basic parties from content
                content = normalized_section['detailed_summary'].lower()
                parties = []
                if 'usfws' in content or 'fish and wildlife' in content:
                    parties.append('U.S. Fish and Wildlife Service')
                if 'importer' in content:
                    parties.append('Importers')
                if 'federal' in content:
                    parties.append('Federal agencies')
                if not parties:
                    parties = ['Government agencies', 'Regulated entities']
                affected_parties = parties
            normalized_section['affected_parties'] = affected_parties[:3]  # Limit to 3
            
            # CRITICAL: Fix empty ev_ids arrays
            ev_ids = section.get('ev_ids', [])
            if not ev_ids and evidence_ids:
                # ALWAYS assign evidence IDs - no empty arrays allowed
                content = normalized_section['detailed_summary'].lower()
                assigned_ids = []
                
                # Try smart assignment based on content keywords
                for eid in evidence_ids:
                    if len(assigned_ids) >= 2:  # Limit to 2 per section
                        break
                    # Find corresponding evidence span
                    matching_span = next((s for s in evidence_spans if s['id'] == eid), None)
                    if matching_span:
                        span_content = matching_span.get('quote', '').lower()
                        # Simple keyword matching
                        if ('enforcement' in content and 'enforcement' in span_content) or \
                           ('funding' in content and ('$' in span_content or 'million' in span_content)) or \
                           ('deadline' in content and ('days' in span_content or 'date' in span_content)) or \
                           ('amendment' in content and 'amend' in span_content):
                            assigned_ids.append(eid)
                
                # FALLBACK: Always assign at least 1-2 evidence IDs (no empty arrays)
                if not assigned_ids and evidence_ids:
                    # Round-robin assignment to ensure every section gets evidence
                    start_idx = i % len(evidence_ids)
                    assigned_ids = [evidence_ids[start_idx]]
                    if len(evidence_ids) > 1:
                        next_idx = (start_idx + 1) % len(evidence_ids)
                        assigned_ids.append(evidence_ids[next_idx])
                
                ev_ids = assigned_ids
                logger.error(f"🔧 Assigned evidence IDs {ev_ids} to section: {normalized_section['title'][:50]}")  # Error level for visibility
            elif ev_ids:
                logger.error(f"✅ Section already has evidence IDs: {ev_ids}")  # Error level for visibility
            else:
                logger.error(f"⚠️ No evidence IDs available to assign")  # Error level for visibility
            
            normalized_section['ev_ids'] = ev_ids
            
            # Preserve any additional metadata
            for key, value in section.items():
                if key not in normalized_section:
                    normalized_section[key] = value
            
            normalized_sections.append(normalized_section)
        
        # Update skeleton with normalized sections
        skeleton['complete_analysis'] = normalized_sections
        
        populated_count = sum(1 for s in normalized_sections if s.get('ev_ids'))
        logger.error(f"✅ Normalized {len(normalized_sections)} sections, {populated_count} have evidence IDs")  # Use error level to ensure visibility
        
        return skeleton

    def _create_details_payload(self, final_analysis: Dict, bill_metadata: Dict, evidence_store: Dict) -> Dict[str, Any]:
        """Create bill_details payload from balanced analysis with HR5-118 citations"""

        complete_analysis = final_analysis.get('complete_analysis', [])
        additional_details = final_analysis.get('additional_details', {})
        user_content = final_analysis.get('user_content', {})

        # Create BILL-SPECIFIC hero summary from bill title and primary sections
        bill_title = bill_metadata.get('title', 'Bill Analysis')
        primary_sections = [s for s in complete_analysis if s.get('importance') == 'primary']

        if primary_sections:
            # Create a comprehensive hero summary from multiple primary sections
            key_actions = []
            affected_parties = set()

            for section in primary_sections[:3]:  # Top 3 primary sections
                actions = section.get('key_actions', [])
                # Handle both string and list formats for key_actions
                if isinstance(actions, str):
                    key_actions.append(actions)
                elif isinstance(actions, list):
                    key_actions.extend(actions[:2])  # 2 actions per section
                
                parties = section.get('affected_parties', [])
                # Handle both string and list formats for affected_parties
                if isinstance(parties, str):
                    affected_parties.add(parties)
                elif isinstance(parties, list):
                    affected_parties.update(parties[:3])  # 3 parties per section

            hero_summary = f"The {bill_title} {self._create_bill_specific_summary(key_actions[:6], list(affected_parties)[:8])}"
        else:
            # Enhanced fallback using bill title context
            bill_title_lower = bill_title.lower()
            if 'amendment' in bill_title_lower:
                hero_summary = f"The {bill_title} modifies existing laws with new enforcement mechanisms, penalties, and regulatory requirements."
            elif 'act' in bill_title_lower and any(word in bill_title_lower for word in ['enforcement', 'compliance', 'penalty']):
                hero_summary = f"The {bill_title} establishes new enforcement powers, compliance standards, and penalties for violations."
            elif 'appropriation' in bill_title_lower or 'funding' in bill_title_lower:
                hero_summary = f"The {bill_title} allocates federal funding and establishes spending priorities for specific programs and agencies."
            else:
                hero_summary = f"The {bill_title} establishes new legal requirements, enforcement mechanisms, and regulatory standards with significant implications for federal and state compliance."

        # Create POPULATED overview sections from complete_analysis
        what_does_content = self._extract_what_does(complete_analysis)
        who_affects_content = self._extract_who_affects(complete_analysis)
        why_matters_content = self._extract_why_matters(complete_analysis, bill_title)

        overview = {
            "what_does": {"content": what_does_content, "citations": self._extract_citations_for_content(complete_analysis[:3], evidence_store)},
            "who_affects": {"content": who_affects_content, "citations": self._extract_citations_for_content(complete_analysis[:3], evidence_store)},
            "why_matters": {"content": why_matters_content, "citations": self._extract_citations_for_content(complete_analysis[:3], evidence_store)},
            "complete_analysis": complete_analysis,  # This is what the user expects to see!
            "additional_details": additional_details
        }

        # Create positions from user content
        positions = {
            "support_reasons": user_content.get('support_reasons', []),
            "oppose_reasons": user_content.get('oppose_reasons', []),
            "amend_reasons": user_content.get('amend_suggestions', [])
        }

        # Extract hero summary citations from primary sections with real evidence
        hero_citations = self._extract_hero_citations(primary_sections[:2], evidence_store)

        return {
            "hero_summary": hero_summary,
            "hero_summary_citations": hero_citations,
            "overview": overview,
            "positions": positions,
            "message_templates": user_content.get('message_templates', {}),
            "tags": self._extract_tags_from_analysis(complete_analysis),
            "other_details": []
        }

    def _resolve_evidence_citations(self, analysis: Dict, evidence_store: Dict) -> Dict:
        """Resolve ev_ids to full citations with headings/anchors - HR5-118 QUALITY"""
        def resolve_section_citations(section):
            if isinstance(section, dict):
                ev_ids = section.get('ev_ids', [])
                if ev_ids:
                    # Map ev_ids to real evidence spans
                    citations = []
                    for ev_id in ev_ids[:3]:  # Limit to 3 citations per section
                        if ev_id in evidence_store:
                            span = evidence_store[ev_id]
                            citations.append({
                                "quote": span.get('quote', '')[:100],  # Real quote from bill
                                "heading": span.get('heading', 'Unknown'),
                                "anchor_id": f"ev-{ev_id}",
                                "start_offset": span.get('start_offset', 0),
                                "end_offset": span.get('end_offset', 0)
                            })
                    if citations:
                        section['citations'] = citations
                # Recursively process nested structures
                for key, value in section.items():
                    if isinstance(value, (dict, list)):
                        section[key] = resolve_section_citations(value)
            elif isinstance(section, list):
                return [resolve_section_citations(item) for item in section]
            return section
        
        return resolve_section_citations(analysis)

    def _extract_free_enrichments(self, evidence_spans: List[Dict]) -> Dict:
        """Extract additional details deterministically from evidence spans"""

        enrichments = {
            'mandates_table': [],
            'penalties_table': [],
            'funding_table': [],
            'deadlines': [],
            'reporting_requirements': []
        }

        for span in evidence_spans:
            quote = span['quote'].lower()

            # Extract mandates
            if any(word in quote for word in ['shall', 'must', 'required']):
                enrichments['mandates_table'].append({
                    'requirement': span['quote'][:100] + '...',
                    'source': span['heading'],
                    'evidence_id': span['id']
                })

            # Extract penalties
            if any(word in quote for word in ['penalty', 'fine', 'violation']):
                amount_match = re.search(r'\$[\d,]+', span['quote'])
                enrichments['penalties_table'].append({
                    'violation': span['heading'],
                    'penalty': amount_match.group(0) if amount_match else 'Unspecified',
                    'evidence_id': span['id']
                })

            # Extract funding
            if any(word in quote for word in ['appropriated', 'authorized', 'funding']):
                amount_match = re.search(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', span['quote'])
                enrichments['funding_table'].append({
                    'purpose': span['heading'],
                    'amount': amount_match.group(0) if amount_match else 'Unspecified',
                    'evidence_id': span['id']
                })

        # Limit each category to prevent bloat
        for key in enrichments:
            enrichments[key] = enrichments[key][:5]

        return enrichments

    def _ensure_evidence_ids(self, evidence_spans: List[Dict]) -> List[Dict]:
        """Ensure all evidence spans have IDs"""
        import uuid

        for i, span in enumerate(evidence_spans):
            if 'id' not in span:
                span['id'] = f"span_{i}_{str(uuid.uuid4())[:8]}"

        return evidence_spans

    def _create_bill_specific_summary(self, key_actions: List[str], affected_parties: List[str]) -> str:
        """Create a bill-specific summary from key actions and affected parties - ENHANCED VERSION"""
        
        # Enhanced fallback - still specific but not generic
        if not key_actions:
            if affected_parties:
                parties_text = ", ".join(affected_parties[:3])
                return f"establishes new requirements and enforcement mechanisms affecting {parties_text}."
            else:
                return "establishes new legal requirements, enforcement mechanisms, and regulatory standards."

        # Clean and enhance actions
        enhanced_actions = []
        for action in key_actions[:3]:  # Limit to top 3 actions
            action = action.strip()
            # Enhance generic actions with more specificity
            if action.lower().startswith('implement'):
                enhanced_actions.append(action.replace('implement', 'implement new'))
            elif action.lower().startswith('establish'):
                enhanced_actions.append(action)
            elif action.lower().startswith('review'):
                enhanced_actions.append(action.replace('review', 'enforce compliance with'))
            elif action.lower() == 'allocate specified funding':
                enhanced_actions.append('allocate federal funding for enforcement and compliance')
            elif action.lower() == 'meet implementation deadline':
                enhanced_actions.append('meet strict implementation deadlines with penalties')
            else:
                enhanced_actions.append(action)

        # Create action summary with better flow
        if len(enhanced_actions) >= 3:
            action_summary = f"{enhanced_actions[0].lower()}, {enhanced_actions[1].lower()}, and {enhanced_actions[2].lower()}"
        elif len(enhanced_actions) == 2:
            action_summary = f"{enhanced_actions[0].lower()} and {enhanced_actions[1].lower()}"
        else:
            action_summary = enhanced_actions[0].lower()

        # Add affected parties with more detail
        if affected_parties:
            # Clean party names and add context
            clean_parties = []
            for party in affected_parties[:3]:  # Limit to 3 parties
                if 'government' in party.lower() and 'agencies' in party.lower():
                    clean_parties.append('federal agencies')
                elif 'regulated entities' in party.lower():
                    clean_parties.append('regulated businesses and entities')
                else:
                    clean_parties.append(party.lower())
            
            parties_text = ", ".join(clean_parties)
            return f"{action_summary}, with significant impacts on {parties_text}."
        else:
            return f"{action_summary}, with broad regulatory and enforcement implications."

    def _extract_what_does(self, complete_analysis: List[Dict]) -> str:
        """Extract what the bill does from complete analysis"""
        primary_actions = []
        for section in complete_analysis:
            if section.get('importance') == 'primary':
                actions = section.get('key_actions', [])
                # Handle both string and list formats for key_actions
                if isinstance(actions, str):
                    primary_actions.append(actions)
                elif isinstance(actions, list):
                    primary_actions.extend(actions[:2])  # 2 actions per primary section

        if not primary_actions:
            return "This bill contains comprehensive legislative provisions."

        # Create a coherent summary
        if len(primary_actions) >= 4:
            return f"This bill {primary_actions[0].lower()}, {primary_actions[1].lower()}, {primary_actions[2].lower()}, and {primary_actions[3].lower()}."
        elif len(primary_actions) >= 2:
            return f"This bill {primary_actions[0].lower()} and {primary_actions[1].lower()}."
        else:
            return f"This bill {primary_actions[0].lower()}."

    def _extract_who_affects(self, complete_analysis: List[Dict]) -> str:
        """Extract who the bill affects from complete analysis"""
        all_parties = set()
        for section in complete_analysis:
            if section.get('importance') in ['primary', 'secondary']:
                parties = section.get('affected_parties', [])
                # Handle both string and list formats for affected_parties
                if isinstance(parties, str):
                    all_parties.add(parties)
                elif isinstance(parties, list):
                    all_parties.update(parties[:3])  # 3 parties per section

        if not all_parties:
            return "This bill affects multiple stakeholders and government entities."

        parties_list = list(all_parties)[:6]  # Limit to 6 parties
        if len(parties_list) >= 3:
            return f"This bill affects {', '.join(parties_list[:-1])}, and {parties_list[-1]}."
        elif len(parties_list) == 2:
            return f"This bill affects {parties_list[0]} and {parties_list[1]}."
        else:
            return f"This bill affects {parties_list[0]}."

    def _extract_why_matters(self, complete_analysis: List[Dict], bill_title: str) -> str:
        """Extract why the bill matters from complete analysis"""
        primary_sections = [s for s in complete_analysis if s.get('importance') == 'primary']

        if not primary_sections:
            return f"The {bill_title} represents significant legislative action with broad implications."

        # Count enforcement mechanisms, funding, and mandates
        enforcement_count = sum(1 for s in primary_sections if any(word in s.get('title', '').lower() for word in ['enforcement', 'penalty', 'compliance']))
        funding_count = sum(1 for s in primary_sections if any(word in s.get('title', '').lower() for word in ['funding', 'appropriation', 'authorization']))
        mandate_count = sum(1 for s in primary_sections if any(word in s.get('title', '').lower() for word in ['requirement', 'mandate', 'compliance']))

        significance_factors = []
        if enforcement_count > 0:
            significance_factors.append("enforcement mechanisms")
        if funding_count > 0:
            significance_factors.append("funding provisions")
        if mandate_count > 0:
            significance_factors.append("compliance requirements")

        if significance_factors:
            factors_text = ", ".join(significance_factors[:-1]) + f", and {significance_factors[-1]}" if len(significance_factors) > 1 else significance_factors[0]
            return f"This bill matters because it establishes {factors_text} that will have significant impact on affected parties and government operations."
        else:
            return f"This bill matters because it introduces {len(primary_sections)} major provisions that will reshape policy and operations in this area."

    def _extract_citations_for_content(self, sections: List[Dict], evidence_store: Dict) -> List[Dict]:
        """Extract real citations from evidence spans - HR5-118 QUALITY"""
        citations = []
        for section in sections[:3]:  # Increased to 3 sections for better coverage
            ev_ids = section.get('ev_ids', [])
            for ev_id in ev_ids[:2]:  # 2 citations per section for comprehensive coverage
                if ev_id in evidence_store:
                    span = evidence_store[ev_id]
                    citations.append({
                        "quote": span.get('quote', '')[:150],  # Real bill quote
                        "heading": span.get('heading', 'Unknown'),
                        "anchor_id": f"ev-{ev_id}",
                        "start_offset": span.get('start_offset', 0),
                        "end_offset": span.get('end_offset', 0)
                    })
        return citations[:6]  # Limit to 6 total citations

    def _extract_hero_citations(self, primary_sections: List[Dict], evidence_store: Dict) -> List[Dict]:
        """Extract real citations for hero summary - HR5-118 QUALITY"""
        citations = []
        for section in primary_sections:
            ev_ids = section.get('ev_ids', [])
            for ev_id in ev_ids[:1]:  # 1 citation per primary section
                if ev_id in evidence_store:
                    span = evidence_store[ev_id]
                    citations.append({
                        "quote": span.get('quote', '')[:100],  # Real bill quote
                        "heading": span.get('heading', 'Unknown'),
                        "anchor_id": f"ev-{ev_id}",
                        "start_offset": span.get('start_offset', 0),
                        "end_offset": span.get('end_offset', 0)
                    })
        return citations[:3]  # Limit to 3 citations

    def _extract_tags_from_analysis(self, complete_analysis: List[Dict]) -> List[str]:
        """Extract relevant tags from the complete analysis"""
        tags = set()

        for section in complete_analysis:
            title = section.get('title', '').lower()

            # Add tags based on section titles and content
            if any(word in title for word in ['funding', 'appropriation', 'authorization']):
                tags.add("Government Funding")
            if any(word in title for word in ['enforcement', 'penalty', 'compliance']):
                tags.add("Regulatory Compliance")
            if any(word in title for word in ['reporting', 'transparency']):
                tags.add("Government Transparency")
            if any(word in title for word in ['deadline', 'timeline', 'implementation']):
                tags.add("Implementation Timeline")
            if any(word in title for word in ['training', 'capacity']):
                tags.add("Capacity Building")
            if any(word in title for word in ['technology', 'information', 'system']):
                tags.add("Technology Requirements")
            if any(word in title for word in ['public', 'participation', 'stakeholder']):
                tags.add("Public Participation")
            if any(word in title for word in ['environmental', 'social', 'impact']):
                tags.add("Impact Assessment")

        # Add default tags if none found
        if not tags:
            tags.update(["Legislative Action", "Government Policy"])

        return list(tags)[:5]  # Limit to 5 tags
    
    async def _improve_analysis_quality(self, analysis: Dict[str, Any], quality_metrics, 
                                      bill_text: str, bill_metadata: Dict, 
                                      evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """
        PHASE 2: Improve analysis quality based on validation results
        Targeted improvements to achieve HR5-118 standards
        """
        logger.info(f"🔧 Attempting quality improvements for {bill_metadata.get('title', 'Unknown')}")
        
        improved_analysis = analysis.copy()
        improvement_cost = 0.0
        
        # Check remaining budget
        bill_status = self.guard.get_bill_status(bill_id)
        remaining_budget = bill_status['budget_remaining']
        
        if remaining_budget < 0.05:  # Need at least $0.05 for improvements
            logger.warning(f"⚠️ Insufficient budget for quality improvements: ${remaining_budget:.4f}")
            return analysis
        
        try:
            # Improvement 1: Enhance specificity if needed
            if quality_metrics.specificity_score < 0.7:
                logger.info("🔍 Improving content specificity...")
                improved_analysis = await self._enhance_specificity(
                    improved_analysis, bill_text, evidence_spans, bill_id
                )
            
            # Improvement 2: Strengthen evidence grounding if needed
            if quality_metrics.evidence_grounding_score < 0.75:
                logger.info("📚 Strengthening evidence grounding...")
                improved_analysis = await self._strengthen_evidence_grounding(
                    improved_analysis, evidence_spans, bill_id
                )
            
            # Improvement 3: Expand comprehensiveness if needed
            if quality_metrics.comprehensiveness_score < 0.7:
                logger.info("📊 Expanding analysis comprehensiveness...")
                improved_analysis = await self._expand_comprehensiveness(
                    improved_analysis, bill_text, evidence_spans, bill_metadata, bill_id
                )
            
            # Improvement 4: Enhance actionability if needed
            if quality_metrics.actionability_score < 0.7:
                logger.info("🎯 Enhancing citizen actionability...")
                improved_analysis = await self._enhance_actionability(
                    improved_analysis, bill_metadata, evidence_spans, bill_id
                )
            
            logger.info("✅ Quality improvement pass completed")
            return improved_analysis
            
        except Exception as e:
            logger.error(f"Quality improvement failed: {e}")
            return analysis  # Return original if improvements fail
    
    async def _enhance_specificity(self, analysis: Dict[str, Any], bill_text: str, 
                                 evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """Enhance content specificity with detailed extraction"""
        
        # Extract specific details from evidence spans
        specific_details = self._extract_specific_details(evidence_spans)
        
        # Enhance complete_analysis sections with specific details
        complete_analysis = analysis.get('complete_analysis', [])
        for section in complete_analysis:
            section_title = section.get('title', '').lower()
            
            # Add specific monetary amounts
            if 'funding' in section_title or 'appropriation' in section_title:
                money_details = [d for d in specific_details['money'] if d]
                if money_details:
                    section['detailed_summary'] += f" Specifically: {'; '.join(money_details[:2])}."
            
            # Add specific deadlines
            if 'implementation' in section_title or 'deadline' in section_title:
                deadline_details = [d for d in specific_details['deadlines'] if d]
                if deadline_details:
                    section['detailed_summary'] += f" Timeline requirements: {'; '.join(deadline_details[:2])}."
            
            # Add specific enforcement mechanisms
            if 'enforcement' in section_title or 'penalty' in section_title:
                penalty_details = [d for d in specific_details['penalties'] if d]
                if penalty_details:
                    section['detailed_summary'] += f" Enforcement measures: {'; '.join(penalty_details[:2])}."
        
        return analysis
    
    async def _strengthen_evidence_grounding(self, analysis: Dict[str, Any], 
                                           evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """Strengthen evidence grounding by ensuring citations"""
        
        complete_analysis = analysis.get('complete_analysis', [])
        
        # Ensure each section has evidence IDs
        for i, section in enumerate(complete_analysis):
            if not section.get('ev_ids'):
                # Assign relevant evidence spans to this section
                section_title = section.get('title', '').lower()
                relevant_spans = []
                
                for span in evidence_spans[:10]:  # Check first 10 spans
                    span_content = (span.get('quote', '') + ' ' + span.get('heading', '')).lower()
                    
                    # Simple relevance matching
                    title_words = section_title.split()
                    relevance_score = sum(1 for word in title_words if word in span_content)
                    
                    if relevance_score > 0:
                        relevant_spans.append((span['id'], relevance_score))
                
                # Sort by relevance and take top 2
                relevant_spans.sort(key=lambda x: x[1], reverse=True)
                section['ev_ids'] = [span_id for span_id, _ in relevant_spans[:2]]
                
                logger.debug(f"Added {len(section['ev_ids'])} evidence citations to section: {section.get('title')}")
        
        return analysis
    
    async def _expand_comprehensiveness(self, analysis: Dict[str, Any], bill_text: str,
                                      evidence_spans: List[Dict], bill_metadata: Dict, 
                                      bill_id: str) -> Dict[str, Any]:
        """Expand analysis comprehensiveness by adding missing key areas"""
        
        complete_analysis = analysis.get('complete_analysis', [])
        existing_titles = [s.get('title', '').lower() for s in complete_analysis]
        
        # Key areas that should be covered
        required_areas = [
            ('funding', 'Funding and Appropriations'),
            ('enforcement', 'Enforcement and Penalties'),
            ('implementation', 'Implementation Requirements'),
            ('reporting', 'Reporting and Oversight'),
            ('compliance', 'Compliance and Standards')
        ]
        
        # Add missing areas if we have evidence for them
        for keyword, title in required_areas:
            if not any(keyword in existing_title for existing_title in existing_titles):
                # Look for evidence spans related to this area
                relevant_spans = []
                for span in evidence_spans:
                    span_content = (span.get('quote', '') + ' ' + span.get('heading', '')).lower()
                    if keyword in span_content:
                        relevant_spans.append(span)
                
                if relevant_spans:
                    # Create new section for this area
                    new_section = {
                        'title': title,
                        'importance': 'secondary',
                        'detailed_summary': f"This bill includes provisions related to {keyword} that require attention.",
                        'key_actions': [f"Review {keyword} requirements", f"Ensure {keyword} compliance"],
                        'affected_parties': ["Government agencies", "Compliance officers"],
                        'ev_ids': [span['id'] for span in relevant_spans[:2]]
                    }
                    complete_analysis.append(new_section)
                    logger.debug(f"Added missing analysis section: {title}")
        
        analysis['complete_analysis'] = complete_analysis
        return analysis
    
    async def _enhance_actionability(self, analysis: Dict[str, Any], bill_metadata: Dict,
                                   evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """Enhance citizen actionability with better position reasoning"""
        
        # Check if we have budget for premium content generation
        bill_status = self.guard.get_bill_status(bill_id)
        if bill_status['budget_remaining'] < 0.03:
            logger.warning("Insufficient budget for actionability enhancement")
            return analysis
        
        # Generate enhanced position reasoning if missing or weak
        positions = analysis.get('positions', {})
        
        if not positions.get('support_reasons') or len(positions.get('support_reasons', [])) < 2:
            # Use efficient model to generate better support reasons
            try:
                complete_analysis = analysis.get('complete_analysis', [])
                primary_sections = [s for s in complete_analysis if s.get('importance') == 'primary']
                
                prompt = self._build_enhanced_actionability_prompt(bill_metadata, primary_sections, 'support')
                
                result = await self.guard.guarded_call(
                    operation_type="reason_generation",
                    ai_function=self._call_openai_json,
                    input_tokens=len(prompt.split()) * 1.3,
                    bill_id=bill_id,
                    prompt=prompt,
                    schema=self._get_enhanced_reasons_schema()
                )
                
                if result.success:
                    enhanced_content = json.loads(result.content)
                    if 'support_reasons' in enhanced_content:
                        positions['support_reasons'] = enhanced_content['support_reasons']
                        logger.debug("Enhanced support reasons generated")
                
            except Exception as e:
                logger.warning(f"Failed to enhance support reasons: {e}")
        
        analysis['positions'] = positions
        return analysis
    
    def _extract_specific_details(self, evidence_spans: List[Dict]) -> Dict[str, List[str]]:
        """Extract specific details from evidence spans"""
        details = {
            'money': [],
            'deadlines': [],
            'penalties': [],
            'requirements': []
        }
        
        for span in evidence_spans:
            quote = span.get('quote', '')
            
            # Extract money amounts
            money_matches = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', quote)
            details['money'].extend(money_matches)
            
            # Extract deadlines
            deadline_matches = re.findall(r'(?:not later than|within \d+|deadline[^.]*)', quote, re.IGNORECASE)
            details['deadlines'].extend(deadline_matches)
            
            # Extract penalties
            penalty_matches = re.findall(r'(?:penalty|fine)[^.]*\$[\d,]+', quote, re.IGNORECASE)
            details['penalties'].extend(penalty_matches)
            
            # Extract specific requirements
            requirement_matches = re.findall(r'(?:shall|must|required to)[^.]*', quote, re.IGNORECASE)
            details['requirements'].extend(requirement_matches)
        
        # Remove duplicates and limit
        for key in details:
            details[key] = list(set(details[key]))[:3]
        
        return details
    
    def _apply_hierarchical_organization(self, analysis: Dict, structure_analysis: Dict) -> Dict:
        """
        PHASE 3.2: Apply hierarchical organization to the analysis using templates
        Organizes sections into nested hierarchies following legal document structure
        """
        complete_analysis = analysis.get('complete_analysis', [])
        section_strategy = structure_analysis.get('section_strategy', [])
        
        # Create hierarchical structure
        hierarchical_sections = []
        
        # Group sections by template type
        template_groups = {}
        for strategy_item in section_strategy:
            template_type = strategy_item['section_type']
            if template_type not in template_groups:
                template_groups[template_type] = {
                    'main_section': strategy_item['main_section'],
                    'subsections': strategy_item['subsections'],
                    'generated_sections': []
                }
        
        # Map generated sections to template groups
        for section in complete_analysis:
            section_title = section.get('title', '').lower()
            best_match = None
            best_score = 0
            
            # Find best template match for this section
            for template_type, group_info in template_groups.items():
                main_title = group_info['main_section']['title'].lower()
                
                # Simple keyword matching
                main_keywords = set(main_title.split())
                section_keywords = set(section_title.split())
                overlap_score = len(main_keywords.intersection(section_keywords))
                
                if overlap_score > best_score:
                    best_score = overlap_score
                    best_match = template_type
            
            # Add to best matching group or create standalone section
            if best_match and best_score > 0:
                template_groups[best_match]['generated_sections'].append(section)
            else:
                # Standalone section
                hierarchical_sections.append(section)
        
        # Build hierarchical structure from template groups
        for template_type, group_info in template_groups.items():
            if group_info['generated_sections']:
                # Create main section
                main_section = {
                    'title': group_info['main_section']['title'],
                    'importance': group_info['main_section']['importance'],
                    'template_type': template_type,
                    'detailed_summary': f"This section covers {template_type} provisions of the bill.",
                    'key_actions': [],
                    'affected_parties': [],
                    'ev_ids': [],
                    'subsections': []
                }
                
                # Add generated sections as subsections
                for section in group_info['generated_sections']:
                    # Combine evidence and details from subsections into main section
                    main_section['key_actions'].extend(section.get('key_actions', [])[:2])
                    main_section['affected_parties'].extend(section.get('affected_parties', [])[:2])
                    main_section['ev_ids'].extend(section.get('ev_ids', [])[:2])
                    
                    # Add as nested subsection
                    main_section['subsections'].append({
                        'title': section.get('title', ''),
                        'importance': section.get('importance', 'technical'),
                        'detailed_summary': section.get('detailed_summary', ''),
                        'key_actions': section.get('key_actions', []),
                        'affected_parties': section.get('affected_parties', []),
                        'ev_ids': section.get('ev_ids', [])
                    })
                
                # Clean up duplicates
                main_section['key_actions'] = list(set(main_section['key_actions']))[:8]
                main_section['affected_parties'] = list(set(main_section['affected_parties']))[:8]
                main_section['ev_ids'] = list(set(main_section['ev_ids']))[:6]
                
                hierarchical_sections.append(main_section)
        
        # Update analysis with hierarchical structure
        hierarchical_analysis = analysis.copy()
        hierarchical_analysis['complete_analysis'] = hierarchical_sections
        hierarchical_analysis['hierarchical_structure'] = True
        hierarchical_analysis['template_organization'] = template_groups
        
        logger.info(f"📋 Hierarchical organization: {len(hierarchical_sections)} main sections with nested subsections")
        
        return hierarchical_analysis
    
    def _apply_optimization_post_processing(self, analysis: Dict, generation_optimization: Dict) -> Dict:
        """
        PHASE 3.4: Apply optimization-guided post-processing to analysis
        """
        complete_analysis = analysis.get('complete_analysis', [])
        optimized_sections = generation_optimization.get('optimized_sections', [])
        
        # Map generated sections to optimization strategy
        for i, section in enumerate(complete_analysis):
            if i < len(optimized_sections):
                optimized_section = optimized_sections[i]
                
                # Add optimization metadata
                section['optimization_metadata'] = {
                    'template_type': optimized_section.template_type,
                    'quality_score': optimized_section.quality_score,
                    'expansion_potential': optimized_section.expansion_potential,
                    'optimization_strategy': generation_optimization['selected_strategy'].strategy_name
                }
                
                # Enhance with content hints if available
                if optimized_section.content_hints:
                    section['content_enhancement_hints'] = optimized_section.content_hints
        
        # Add optimization summary
        analysis['phase3_optimization'] = {
            'strategy_used': generation_optimization['selected_strategy'].strategy_name,
            'target_sections': generation_optimization['target_achievement']['optimized_sections'],
            'achievement_rate': generation_optimization['target_achievement']['achievement_rate'],
            'quality_score': generation_optimization['target_achievement']['quality_score']
        }
        
        logger.info(f"📊 Optimization post-processing: Enhanced {len(complete_analysis)} sections with metadata")
        return analysis
    
    def _get_optimized_skeleton_schema(self, target_sections: int) -> Dict:
        """
        PHASE 3.4: Schema for optimized skeleton analysis
        """
        return {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "minItems": target_sections,
                    "maxItems": min(target_sections + 5, 50),  # Allow slight flexibility
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "detailed_summary": {"type": "string"},
                            "key_actions": {"type": "array", "items": {"type": "string"}},
                            "affected_parties": {"type": "array", "items": {"type": "string"}},
                            "ev_ids": {"type": "array", "items": {"type": "string"}},
                            "legal_precision_score": {"type": "number", "minimum": 0, "maximum": 1},
                            "content_depth": {"type": "string", "enum": ["surface", "detailed", "comprehensive"]}
                        },
                        "required": ["title", "importance", "detailed_summary", "key_actions", "affected_parties", "ev_ids"]
                    }
                }
            },
            "required": ["complete_analysis"]
        }
    
    def _get_enhanced_optimized_skeleton_schema(self, target_sections: int) -> Dict:
        """
        PHASE 3.4: Enhanced schema for optimization-guided analysis with strict requirements
        """
        return {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "minItems": target_sections,
                    "maxItems": min(target_sections + 10, 50),  # Allow more flexibility for enhancement
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "detailed_summary": {"type": "string"},
                            "key_actions": {"type": "array", "items": {"type": "string"}},
                            "affected_parties": {"type": "array", "items": {"type": "string"}},
                            "ev_ids": {"type": "array", "items": {"type": "string"}},
                            "legal_precision_score": {"type": "number", "minimum": 0, "maximum": 1},
                            "content_depth": {"type": "string", "enum": ["surface", "detailed", "comprehensive"]},
                            "statutory_references": {"type": "array", "items": {"type": "string"}},
                            "implementation_complexity": {"type": "string", "enum": ["low", "medium", "high"]}
                        },
                        "required": ["title", "importance", "detailed_summary", "key_actions", "affected_parties", "ev_ids"]
                    }
                }
            },
            "required": ["complete_analysis"]
        }
    
    def _get_template_skeleton_schema(self, estimated_sections: int) -> Dict:
        """
        PHASE 3.2: Schema for template-guided skeleton analysis
        """
        min_sections = max(25, estimated_sections)
        return {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "minItems": min_sections,
                    "maxItems": 50,
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "detailed_summary": {"type": "string"},
                            "key_actions": {"type": "array", "items": {"type": "string"}},
                            "affected_parties": {"type": "array", "items": {"type": "string"}},
                            "ev_ids": {"type": "array", "items": {"type": "string"}},
                            "template_type": {"type": "string"}
                        },
                        "required": ["title", "importance", "detailed_summary", "key_actions", "affected_parties", "ev_ids"]
                    }
                }
            },
            "required": ["complete_analysis"]
        }
    
    def _get_enhanced_template_skeleton_schema(self, target_sections: int) -> Dict:
        """
        PHASE 3.2: Enhanced schema for template-guided hierarchical analysis
        """
        return {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "minItems": target_sections,
                    "maxItems": 50,
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "detailed_summary": {"type": "string"},
                            "key_actions": {"type": "array", "items": {"type": "string"}},
                            "affected_parties": {"type": "array", "items": {"type": "string"}},
                            "ev_ids": {"type": "array", "items": {"type": "string"}},
                            "template_type": {"type": "string"},
                            "hierarchical_level": {"type": "integer", "minimum": 1, "maximum": 3},
                            "parent_section": {"type": "string"}
                        },
                        "required": ["title", "importance", "detailed_summary", "key_actions", "affected_parties", "ev_ids"]
                    }
                }
            },
            "required": ["complete_analysis"]
        }
    
    def _build_enhanced_actionability_prompt(self, bill_metadata: Dict, 
                                           primary_sections: List[Dict], 
                                           reason_type: str) -> str:
        """Build prompt for enhanced actionability content"""
        
        sections_text = "\n".join([
            f"Section: {section.get('title', 'Unknown')}\n"
            f"Summary: {section.get('detailed_summary', '')}\n"
            f"Key Actions: {', '.join(section.get('key_actions', []))}\n"
            for section in primary_sections[:3]
        ])
        
        return f"""
Generate specific, actionable {reason_type} reasons for citizens regarding this bill.
Be concrete, evidence-based, and avoid generic language.

Bill: {bill_metadata.get('title', 'Unknown')}

Key Provisions:
{sections_text}

Generate 3-4 specific {reason_type} reasons that:
- Are grounded in actual bill provisions
- Explain concrete impacts on citizens
- Use specific examples where possible
- Avoid generic phrases like "comprehensive provisions"
- Include evidence IDs for verification

Format each reason with: reason, explanation, and ev_ids array.
"""
    
    def _get_enhanced_reasons_schema(self) -> Dict:
        """Schema for enhanced reason generation"""
        return {
            "type": "object",
            "properties": {
                "support_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "reason": {"type": "string"},
                            "explanation": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                }
            }
        }
    
    def _build_enhanced_skeleton_prompt(self, bill_text: str, bill_metadata: Dict, 
                                      evidence_spans: List[Dict], target_sections: int) -> str:
        """Build enhanced skeleton prompt for more comprehensive section generation"""
        
        evidence_context = "\n".join([
            f"Evidence {span['id']}: {span['heading']}\n"
            f"Quote: {span['quote'][:150]}..."
            for span in evidence_spans[:100]
        ])
        
        return f"""ENHANCED COMPREHENSIVE LEGAL ANALYSIS - TARGET: {target_sections}+ SECTIONS

BILL: {bill_metadata.get('title', 'Unknown')} ({bill_metadata.get('bill_number', 'Unknown')})

CRITICAL INSTRUCTION: You MUST generate AT LEAST {target_sections} detailed sections. This is a MINIMUM requirement.

HR5-118 GOLD STANDARD REQUIREMENTS:
- MANDATORY: Generate {target_sections}-50 comprehensive sections minimum
- Analyze EVERY SEC. provision separately (SEC. 1, SEC. 2, SEC. 3, etc.)
- Break down major paragraphs (1), (2), (3) into separate sections when substantive  
- Include subsections (a), (b), (c) analysis when they contain significant provisions
- Create separate sections for each enforcement mechanism
- Analyze each funding provision separately
- Cover each timeline/deadline requirement

ENHANCED SECTIONING STRATEGY:
1. Title and Purpose Analysis (1 section)
2. Each SEC. provision gets its own section (typically 3-15 sections)
3. Major paragraph breakdowns within each SEC. (5-20 additional sections)
4. Enforcement mechanisms (2-5 sections)
5. Funding and appropriations (1-3 sections)
6. Timeline and implementation (2-4 sections)
7. Affected parties analysis (3-8 sections)
8. Definitions and scope (1-2 sections)
9. Amendment impacts (2-6 sections)
10. Compliance requirements (2-4 sections)

EVIDENCE CONTEXT:
{evidence_context}

BILL TEXT (analyze comprehensively):
{bill_text[:8000]}

Generate a comprehensive analysis with AT LEAST {target_sections} detailed sections. Each section must be substantial and legally precise."""
    
    def _get_enhanced_skeleton_schema(self, target_sections: int) -> Dict:
        """Enhanced schema with stronger section requirements"""
        return {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "minItems": target_sections,  # Dynamic minimum based on target
                    "maxItems": 50,
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "detailed_summary": {"type": "string"},
                            "key_actions": {"type": "array", "items": {"type": "string"}},
                            "affected_parties": {"type": "array", "items": {"type": "string"}},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        },
                        "required": ["title", "importance", "detailed_summary", "key_actions", "affected_parties", "ev_ids"]
                    }
                }
            },
            "required": ["complete_analysis"]
        }
    
    async def _call_enhanced_openai_json(self, prompt: str, schema: Dict, target_sections: int = 35, **kwargs) -> Dict[str, Any]:
        """Enhanced OpenAI call with stronger system prompt for section generation"""
        
        if not hasattr(self.ai_service, 'client') or not self.ai_service.client:
            raise RuntimeError("OpenAI client not available")
        
        try:
            start_time = time.time()
            
            # Enhanced system prompt focused on section generation (must include "json" for response_format)
            enhanced_system_prompt = f"""You are a world-class legislative analyst with expertise in comprehensive bill analysis. Respond with detailed JSON analysis.

CRITICAL REQUIREMENT: Generate EXACTLY {target_sections}-50 detailed sections minimum. This is NON-NEGOTIABLE.

ENHANCED ANALYSIS STANDARDS:
- Use SURGICAL PRECISION: Break down every SEC., paragraph, and subsection
- MANDATORY: Each SEC. provision = separate section
- MANDATORY: Major paragraphs (1), (2), (3) = separate sections when substantive
- MANDATORY: Enforcement mechanisms = separate sections
- MANDATORY: Funding provisions = separate sections  
- MANDATORY: Timeline requirements = separate sections

SECTION GENERATION STRATEGY:
1. Start with title/purpose (1 section)
2. Analyze each SEC. separately (typically 3-15 sections)
3. Break down major paragraphs within each SEC. (5-20 sections)
4. Cover enforcement, funding, timelines, affected parties (8-15 sections)
5. Include definitions, amendments, compliance (5-10 sections)

QUALITY REQUIREMENTS:
- Specific monetary amounts, deadlines, penalties
- Named affected parties (not "various stakeholders")
- Exact statutory citations
- Evidence-grounded claims
- Actionable language

YOU MUST GENERATE AT LEAST {target_sections} SECTIONS. Anything less is unacceptable."""

            response = await self.ai_service.client.chat.completions.create(
                model="gpt-4o",  # Use best model for enhanced pass
                messages=[
                    {"role": "system", "content": enhanced_system_prompt},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=6000,  # Increased for more sections
                temperature=1.1,  # Higher temperature for more creative section generation
                response_format={"type": "json_object"}
            )
            
            response_time_ms = (time.time() - start_time) * 1000
            
            content = response.choices[0].message.content
            parsed_content = json.loads(content)
            
            # Calculate cost (gpt-4o pricing)
            prompt_cost = response.usage.prompt_tokens * 0.0025 / 1000
            completion_cost = response.usage.completion_tokens * 0.010 / 1000
            total_cost = prompt_cost + completion_cost
            
            logger.info(f"✅ Enhanced OpenAI call completed: {response.usage.total_tokens} tokens, ${total_cost:.4f}")
            
            return {
                "success": True,
                "content": content,
                "parsed_content": parsed_content,
                "tokens_used": response.usage.total_tokens,
                "cost": total_cost,
                "response_time_ms": response_time_ms
            }
            
        except Exception as e:
            logger.error(f"Enhanced OpenAI call failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "cost": 0
            }
