# app/services/bill_details_service.py
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
import re
import logging

logger = logging.getLogger(__name__)

from app.models.bill_details import BillDetails
from app.models.bill import Bill
from app.services.text_citation_service import TextCitationService


class BillDetailsService:
    def __init__(self, db: Session):
        self.db = db
        self.text_service = TextCitationService()

    def _slugify(self, bill: Bill) -> str:
        """Generate canonical slug like hr5-118 from bill_number and session_year."""
        raw = (str(getattr(bill, 'bill_number', '') or '')).lower()
        # Normalize strings like 'h.r.5' -> 'hr5', keep only letters/digits
        letters = ''.join(ch for ch in raw if ch.isalpha())
        digits = ''.join(ch for ch in raw if ch.isdigit())
        if not letters and raw:
            # Fallback: use raw prefix of alpha chars
            letters = ''.join(ch for ch in raw if ch.isalpha())
        base = f"{letters}{digits}-{bill.session_year}" if digits else f"{raw}-{bill.session_year}"
        base = base.lower()
        base = re.sub(r"[^a-z0-9\-]", "-", base)
        base = re.sub(r"-+", "-", base).strip('-')
        return base


    def _generate_candidate_quotes(self, content, full_text: str, max_quotes: int = 2) -> List[str]:
        """Heuristic extraction of short phrases from content that appear in full_text.
        Picks up to max_quotes quotes per piece of content.
        """
        quotes: List[str] = []
        if not content or not full_text:
            return quotes

        # Handle case where content might be a dict or other structure
        if isinstance(content, dict):
            content = content.get("content", str(content))
        content = str(content)

        text_lc = full_text.lower()
        # Split content into sentences roughly by punctuation
        sentences = re.split(r"(?<=[\.!?])\s+", content)
        for sent in sentences:
            if len(quotes) >= max_quotes:
                break
            words = [w for w in re.split(r"\s+", sent.strip()) if w]
            # Try windows from 12 words down to 3 to increase match chance
            for win in range(12, 2, -1):
                if len(quotes) >= max_quotes:
                    break
                for i in range(0, max(0, len(words) - win + 1)):
                    frag = " ".join(words[i:i+win])
                    frag_norm = re.sub(r"\s+", " ", frag).strip().lower()
                    idx = text_lc.find(frag_norm)
                    if idx != -1:
                        # Use approximate original casing slice
                        quote = full_text[idx: idx + len(frag_norm)]
                        if quote not in quotes and len(quote.split()) >= 3:
                            quotes.append(quote)
                            break
        return quotes

    def _enrich_citations(self, details_payload: Dict[str, Any], full_text: str, source_index_items):
        """Add citations to sections/claims by binding quotes to the bill text.
        - If citations exist but lack offsets, bind them (preferred path)
        - If citations are missing, generate candidate quotes heuristically and bind
        """
        def bind_quote_to_citation_obj(cite_obj: Dict[str, Any]):
            q = (cite_obj or {}).get("quote") or ""
            if not q:
                return None
            b = self.text_service.bind_quote(full_text, source_index_items, q)
            if not b:
                return None
            return {
                "quote": b.quote,
                "start_offset": b.start_offset,
                "end_offset": b.end_offset,
                "heading": b.heading,
                "anchor_id": b.anchor_id,
            }

        def bind_quotes_into_list(quotes: List[str], max_bind: int = 4):
            bindings = []
            for q in quotes:
                if len(bindings) >= max_bind:
                    break
                b = self.text_service.bind_quote(full_text, source_index_items, q)
                if b:
                    bindings.append({
                        "quote": b.quote,
                        "start_offset": b.start_offset,
                        "end_offset": b.end_offset,
                        "heading": b.heading,
                        "anchor_id": b.anchor_id,
                    })
            return bindings

        def enrich_or_bind_section(section: Optional[Dict[str, Any]]):
            if not section or not isinstance(section, dict):
                return
            citations = section.get("citations")
            if isinstance(citations, list) and len(citations) > 0:
                # Bind any citation objects that lack offsets
                new_cites = []
                for c in citations:
                    if isinstance(c, dict) and ("start_offset" not in c or c.get("start_offset") is None):
                        bound = bind_quote_to_citation_obj(c)
                        if bound:
                            new_cites.append(bound)
                    elif isinstance(c, dict) and all(k in c for k in ("quote","start_offset","end_offset")):
                        new_cites.append(c)
                if new_cites:
                    section["citations"] = new_cites
                    return
                # Fall through to heuristic if nothing bound
            content = section.get("content") or ""
            quotes = self._generate_candidate_quotes(content, full_text, max_quotes=4)
            section["citations"] = bind_quotes_into_list(quotes, max_bind=4)

        # Enrich hero_summary citations - FORCE GENERATION
        hero = details_payload.get("hero_summary") or ""
        if hero:
            # Always generate citations for hero summary
            h_quotes = self._generate_candidate_quotes(hero, full_text, max_quotes=3)
            hero_citations = bind_quotes_into_list(h_quotes, max_bind=3)
            details_payload["hero_summary_citations"] = hero_citations
            print(f"DEBUG: Generated {len(hero_citations)} hero citations from {len(h_quotes)} quotes")

        overview = details_payload.get("overview") or {}
        for key in ["what_does", "who_affects", "why_matters", "cost_impact"]:
            section = overview.get(key)
            if section:
                enrich_or_bind_section(section)
                print(f"DEBUG: {key} citations: {len(section.get('citations', []))}")

        if isinstance(overview.get("key_provisions"), list):
            for i, item in enumerate(overview["key_provisions"]):
                enrich_or_bind_section(item)
                print(f"DEBUG: key_provision {i} citations: {len(item.get('citations', []))}")

        if isinstance(overview.get("timeline"), list):
            for i, item in enumerate(overview["timeline"]):
                enrich_or_bind_section(item)
                print(f"DEBUG: timeline {i} citations: {len(item.get('citations', []))}")

        positions = details_payload.get("positions") or {}
        for key in ["support_reasons", "oppose_reasons", "amend_reasons"]:
            if isinstance(positions.get(key), list):
                for i, item in enumerate(positions[key]):
                    # Force citation generation for all position reasons
                    content_text = (item.get("claim") or "") + " " + (item.get("justification") or "")
                    quotes = self._generate_candidate_quotes(content_text, full_text, max_quotes=3)
                    item["citations"] = bind_quotes_into_list(quotes, max_bind=3)
                    print(f"DEBUG: {key}[{i}] citations: {len(item.get('citations', []))}")

        other_details = details_payload.get("other_details")
        if isinstance(other_details, list):
            for item in other_details:
                enrich_or_bind_section(item)

        details_payload["overview"] = overview
        details_payload["positions"] = positions
        return details_payload

    def _compute_metrics(self, details_payload: Dict[str, Any]) -> Dict[str, Any]:
        # Compute unverified count and simple coverage ratio (sections with any citations / sections with content)
        total_sections = 0
        verified_sections = 0

        def walk(obj: Any):
            nonlocal total_sections, verified_sections
            if isinstance(obj, dict):
                # count only content-bearing dicts
                if "content" in obj:
                    total_sections += 1
                    cites = obj.get("citations") or []
                    if isinstance(cites, list) and len(cites) > 0:
                        verified_sections += 1
                for v in obj.values():
                    walk(v)
            elif isinstance(obj, list):
                for x in obj:
                    walk(x)

        walk(details_payload)
        coverage = (verified_sections / total_sections) if total_sections > 0 else 0.0

        # Unverified count (content dicts with empty citations)
        def count_unverified(obj: Any) -> int:
            if isinstance(obj, dict):
                total = 0
                for _, v in obj.items():
                    total += count_unverified(v)
                if 'citations' in obj and isinstance(obj['citations'], list) and len(obj['citations']) == 0 and 'content' in obj:
                    return total + 1
                return total
            if isinstance(obj, list):
                return sum(count_unverified(x) for x in obj)
            return 0

        unverified = count_unverified(details_payload)
        return {"coverage_ratio": round(coverage, 3), "unverified_count": unverified}

    def create_or_update_details_by_id(self, bill_id: str, bill_text: str, details_payload: Dict[str, Any]) -> BillDetails:
        """Create or update bill_details using bill_id (safer than ORM object)"""
        # Validate bill_id exists
        bill = self.db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            raise ValueError(f"Bill with ID {bill_id} not found")

        return self.create_or_update_details(bill, bill_text, details_payload)

    def create_or_update_details(
        self,
        bill: Bill,
        full_text: str,
        details_payload: Dict[str, Any],
    ) -> BillDetails:
        # Build source index
        source_index_items = self.text_service.build_source_index(full_text)
        source_index = [
            {
                "heading": it.heading,
                "start_offset": it.start_offset,
                "end_offset": it.end_offset,
                "anchor_id": it.anchor_id,
                "summary": (full_text[it.start_offset:it.end_offset] or "").strip()[:200]
            }
            for it in source_index_items
        ]

        # Enrich missing citations
        details_payload = self._enrich_citations(details_payload, full_text, source_index_items)

        # Compute metrics
        metrics = self._compute_metrics(details_payload)

        # Decide review flag (require review if coverage < 0.6)
        needs_review = True if metrics.get("coverage_ratio", 0.0) < 0.6 else False

        # Find existing
        existing: Optional[BillDetails] = self.db.query(BillDetails).filter(BillDetails.bill_id == bill.id).first()
        if existing:
            existing.hero_summary = details_payload.get("hero_summary")
            existing.hero_summary_citations = details_payload.get("hero_summary_citations")
            existing.overview = details_payload.get("overview")
            existing.positions = details_payload.get("positions")
            existing.message_templates = details_payload.get("message_templates")
            existing.tags = details_payload.get("tags")
            existing.other_details = details_payload.get("other_details")
            existing.source_index = source_index
            existing.metrics = metrics
            print(f"DEBUG: Saving hero_summary_citations: {len(details_payload.get('hero_summary_citations', []))}")
            # Ensure canonical, normalized slug and URL
            canonical_slug = self._slugify(bill)
            if not existing.seo_slug or existing.seo_slug != canonical_slug:
                existing.seo_slug = canonical_slug
            if not existing.canonical_url or not existing.canonical_url.endswith(existing.seo_slug):
                existing.canonical_url = f"https://modernaction.io/bills/{existing.seo_slug}"
            existing.needs_human_review = needs_review
            self.db.commit()
            self.db.refresh(existing)
            return existing

        # Create new - but first ensure bill has valid ID
        if not bill.id:
            # Try to refresh the bill object to get the ID
            try:
                self.db.refresh(bill)
                if not bill.id:
                    raise ValueError(f"Bill {getattr(bill, 'bill_number', 'unknown')} has no valid ID - cannot create bill_details")
            except Exception as refresh_error:
                raise ValueError(f"Bill {getattr(bill, 'bill_number', 'unknown')} has no valid ID and refresh failed: {refresh_error}")

        # Apply quality gates before creating bill_details
        quality_check = self._apply_quality_gates(details_payload)
        if not quality_check["passed"]:
            # FAIL-CLOSED: Set needs_human_review=true for bad content
            logger.warning(f"Quality gates failed: {quality_check['errors']}")
            needs_review = True
        else:
            needs_review = False

        details = BillDetails(
            bill_id=bill.id,
            seo_slug=self._slugify(bill),
            seo_title=bill.title[:512] if bill.title else None,
            seo_meta_description=(details_payload.get("hero_summary") or bill.summary or bill.ai_summary or "")[:300],
            canonical_url=None,  # will be filled by frontend or here below
            hero_summary=details_payload.get("hero_summary"),
            hero_summary_citations=details_payload.get("hero_summary_citations"),
            overview=details_payload.get("overview"),
            positions=details_payload.get("positions"),
            message_templates=details_payload.get("message_templates"),
            tags=details_payload.get("tags"),
            other_details=details_payload.get("other_details"),
            source_index=source_index,
            needs_human_review=needs_review,  # Set by quality gates
            metrics=metrics,
        )
        details.canonical_url = f"https://modernaction.io/bills/{details.seo_slug}"
        self.db.add(details)
        self.db.commit()
        self.db.refresh(details)
        return details

    def _apply_quality_gates(self, details_payload: Dict[str, Any]) -> Dict[str, Any]:
        """Apply quality gates to determine if content should be published"""
        errors = []

        # Gate 1: Evidence coverage - hero_summary must have citations
        hero_citations = details_payload.get("hero_summary_citations", [])
        if not hero_citations or len(hero_citations) == 0:
            errors.append("No hero_summary_citations provided")
        else:
            # Check for null headings/anchor_ids (the main issue we're fixing)
            for i, citation in enumerate(hero_citations):
                if not citation.get("heading"):
                    errors.append(f"Citation {i}: null/empty heading")
                if not citation.get("anchor_id"):
                    errors.append(f"Citation {i}: null/empty anchor_id")
                if not citation.get("quote") or len(citation.get("quote", "")) < 10:
                    errors.append(f"Citation {i}: quote too short or empty")

        # Gate 2: Content specificity - check for generic phrases
        hero_summary = details_payload.get("hero_summary", "")
        generic_phrases = [
            "effective immediately upon enactment",
            "technical provisions and administrative details",
            "various provisions",
            "other requirements"
        ]
        for phrase in generic_phrases:
            if phrase.lower() in hero_summary.lower():
                errors.append(f"Generic phrase detected: '{phrase}'")

        # Gate 3: Overview content quality
        overview = details_payload.get("overview", {})
        if isinstance(overview, dict):
            what_does = overview.get("what_does", {})
            if isinstance(what_does, dict):
                citations = what_does.get("citations", [])
                if not citations:
                    errors.append("what_does section has no citations")

        # Calculate quality score
        total_checks = 6  # Number of quality checks
        failed_checks = len(errors)
        quality_score = max(0, (total_checks - failed_checks) / total_checks)

        # Determine if content passes quality gates
        passed = len(errors) == 0 and quality_score >= 0.8

        logger.info(f"Quality gates: {len(errors)} errors, score: {quality_score:.2f}, passed: {passed}")

        return {
            "passed": passed,
            "errors": errors,
            "quality_score": quality_score,
            "total_checks": total_checks,
            "failed_checks": failed_checks
        }

