"""
AI Budget Guard - Hard caps and timeouts for all LLM calls
Prevents runaway costs and ensures reliable performance
"""

import asyncio
import time
import json
import logging
import uuid
from typing import Dict, Any, Optional, Callable
from contextlib import asynccontextmanager
from dataclasses import dataclass

from app.core.ai_routing import get_ai_router, ModelConfig
from app.models.ai_usage import AIUsageLog
from app.db.database import SessionLocal

logger = logging.getLogger(__name__)

@dataclass
class CallResult:
    """Result of a guarded AI call"""
    success: bool
    content: Optional[str] = None
    tokens_used: int = 0
    cost: float = 0.0
    error: Optional[str] = None
    timeout: bool = False
    budget_blocked: bool = False

class BudgetTracker:
    """Track spending per bill to enforce caps"""
    
    def __init__(self):
        self.bill_spending: Dict[str, float] = {}
        self.bill_start_time: Dict[str, float] = {}
    
    def start_bill(self, bill_id: str):
        """Start tracking a new bill"""
        self.bill_spending[bill_id] = 0.0
        self.bill_start_time[bill_id] = time.time()
    
    def add_cost(self, bill_id: str, cost: float):
        """Add cost to bill tracking"""
        if bill_id not in self.bill_spending:
            self.bill_spending[bill_id] = 0.0
        self.bill_spending[bill_id] += cost
    
    def get_spent(self, bill_id: str) -> float:
        """Get amount spent on bill so far"""
        return self.bill_spending.get(bill_id, 0.0)
    
    def get_elapsed_ms(self, bill_id: str) -> float:
        """Get elapsed time for bill in milliseconds"""
        if bill_id not in self.bill_start_time:
            return 0.0
        return (time.time() - self.bill_start_time[bill_id]) * 1000
    
    def cleanup_bill(self, bill_id: str):
        """Clean up tracking for completed bill"""
        self.bill_spending.pop(bill_id, None)
        self.bill_start_time.pop(bill_id, None)

# Global budget tracker
budget_tracker = BudgetTracker()

class AIGuard:
    """
    Guard wrapper for all LLM calls
    Enforces budget caps, timeouts, and retry policies
    """
    
    def __init__(self):
        self.router = get_ai_router()
    
    async def guarded_call(
        self,
        operation_type: str,
        ai_function: Callable,
        input_tokens: int,
        bill_id: Optional[str] = None,
        **kwargs
    ) -> CallResult:
        """
        Execute AI call with full guard protection
        
        Args:
            operation_type: Type of operation (from AI router)
            ai_function: Async function to call
            input_tokens: Estimated input tokens
            bill_id: Bill ID for budget tracking
            **kwargs: Arguments to pass to ai_function
        """
        
        config = self.router.get_config(operation_type)
        projected_cost = self.router.estimate_cost(operation_type, input_tokens)
        
        # Budget check
        if bill_id:
            spent_so_far = budget_tracker.get_spent(bill_id)
            elapsed_ms = budget_tracker.get_elapsed_ms(bill_id)
            
            # Check bill deadline
            if elapsed_ms > self.router.bill_deadline_ms:
                logger.warning(f"Bill {bill_id} exceeded deadline ({elapsed_ms}ms)")
                return CallResult(
                    success=False,
                    error=f"Bill deadline exceeded ({elapsed_ms}ms)",
                    timeout=True
                )
            
            # Check budget
            if not self.router.plan_or_block(projected_cost, spent_so_far):
                logger.warning(f"Budget blocked: {projected_cost} + {spent_so_far} > {self.router.bill_cost_cap}")
                return CallResult(
                    success=False,
                    error=f"Budget cap exceeded: ${spent_so_far + projected_cost:.4f} > ${self.router.bill_cost_cap}",
                    budget_blocked=True
                )
        
        # Execute with timeout and retry
        start_time = time.time()
        try:
            result = await self._execute_with_timeout(
                ai_function,
                config.timeout_ms,
                **kwargs
            )

            # Calculate actual metrics
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000
            actual_tokens = result.get('tokens_used', input_tokens + config.max_tokens)
            actual_cost = result.get('cost', projected_cost)

            # Track spending
            if bill_id:
                budget_tracker.add_cost(bill_id, actual_cost)

            # Sanitize JSON if needed
            if config.use_json_mode and result.get('content'):
                result['content'] = self._sanitize_json(result['content'])

            # Log comprehensive AI usage data
            prompt_text = kwargs.get('prompt', '')
            prompt_length = len(str(prompt_text)) if prompt_text else 0

            self._log_ai_usage(
                operation_type=operation_type,
                operation_subtype=kwargs.get('operation_subtype', ''),
                bill_id=bill_id,
                model_name=config.model,
                prompt_tokens=result.get('prompt_tokens', input_tokens),
                completion_tokens=result.get('completion_tokens', config.max_tokens),
                total_tokens=actual_tokens,
                prompt_cost=result.get('prompt_cost', projected_cost * 0.7),
                completion_cost=result.get('completion_cost', projected_cost * 0.3),
                total_cost=actual_cost,
                response_time_ms=response_time_ms,
                success=True,
                prompt_length=prompt_length,
                response_length=len(result.get('content', '')),
                user_id=kwargs.get('user_id'),
                session_id=kwargs.get('session_id')
            )

            return CallResult(
                success=True,
                content=result.get('content'),
                tokens_used=actual_tokens,
                cost=actual_cost
            )
            
        except asyncio.TimeoutError:
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000
            error_msg = f"Timeout after {config.timeout_ms}ms"

            logger.warning(f"AI call timeout after {config.timeout_ms}ms")

            # Log timeout failure
            prompt_text = kwargs.get('prompt', '')
            prompt_length = len(str(prompt_text)) if prompt_text else 0

            self._log_ai_usage(
                operation_type=operation_type,
                operation_subtype=kwargs.get('operation_subtype', ''),
                bill_id=bill_id,
                model_name=config.model,
                prompt_tokens=input_tokens,
                completion_tokens=0,
                total_tokens=input_tokens,
                prompt_cost=0.0,
                completion_cost=0.0,
                total_cost=0.0,
                response_time_ms=response_time_ms,
                success=False,
                error_message=error_msg,
                prompt_length=prompt_length,
                response_length=0,
                user_id=kwargs.get('user_id'),
                session_id=kwargs.get('session_id')
            )

            return CallResult(
                success=False,
                error=error_msg,
                timeout=True
            )
        except Exception as e:
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000
            error_msg = str(e)

            logger.error(f"AI call failed: {e}")

            # Log general failure
            prompt_text = kwargs.get('prompt', '')
            prompt_length = len(str(prompt_text)) if prompt_text else 0

            self._log_ai_usage(
                operation_type=operation_type,
                operation_subtype=kwargs.get('operation_subtype', ''),
                bill_id=bill_id,
                model_name=config.model,
                prompt_tokens=input_tokens,
                completion_tokens=0,
                total_tokens=input_tokens,
                prompt_cost=0.0,
                completion_cost=0.0,
                total_cost=0.0,
                response_time_ms=response_time_ms,
                success=False,
                error_message=error_msg,
                prompt_length=prompt_length,
                response_length=0,
                user_id=kwargs.get('user_id'),
                session_id=kwargs.get('session_id')
            )

            return CallResult(
                success=False,
                error=error_msg
            )
    
    async def _execute_with_timeout(
        self,
        ai_function: Callable,
        timeout_ms: int,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute AI function with timeout"""
        timeout_seconds = timeout_ms / 1000.0
        
        return await asyncio.wait_for(
            ai_function(**kwargs),
            timeout=timeout_seconds
        )
    
    def _sanitize_json(self, content: str) -> str:
        """
        Sanitize JSON response to ensure valid parsing
        Handles common issues: code fences, extra text, JSON5 syntax
        """
        if not content:
            return content
        
        # Remove code fences
        content = content.strip()
        if content.startswith('```'):
            lines = content.split('\n')
            # Remove first line if it's ```json or ```
            if lines[0].startswith('```'):
                lines = lines[1:]
            # Remove last line if it's ```
            if lines and lines[-1].strip() == '```':
                lines = lines[:-1]
            content = '\n'.join(lines)
        
        # Find JSON object boundaries
        start_idx = content.find('{')
        if start_idx == -1:
            return content
        
        # Find matching closing brace
        brace_count = 0
        end_idx = -1
        for i in range(start_idx, len(content)):
            if content[i] == '{':
                brace_count += 1
            elif content[i] == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i + 1
                    break
        
        if end_idx == -1:
            return content
        
        # Extract JSON portion
        json_content = content[start_idx:end_idx]
        
        # Try to parse and re-serialize to ensure validity
        try:
            parsed = json.loads(json_content)
            return json.dumps(parsed, ensure_ascii=False)
        except json.JSONDecodeError:
            # Return original if we can't parse
            return json_content
    
    @asynccontextmanager
    async def bill_context(self, bill_id: str):
        """Context manager for bill-level budget tracking"""
        budget_tracker.start_bill(bill_id)
        try:
            yield budget_tracker
        finally:
            budget_tracker.cleanup_bill(bill_id)
    
    def get_bill_status(self, bill_id: str) -> Dict[str, Any]:
        """Get current status for a bill"""
        return {
            'spent': budget_tracker.get_spent(bill_id),
            'elapsed_ms': budget_tracker.get_elapsed_ms(bill_id),
            'budget_cap': self.router.bill_cost_cap,
            'deadline_ms': self.router.bill_deadline_ms,
            'budget_remaining': self.router.bill_cost_cap - budget_tracker.get_spent(bill_id),
            'time_remaining_ms': self.router.bill_deadline_ms - budget_tracker.get_elapsed_ms(bill_id)
        }

    def _log_ai_usage(self, operation_type: str, operation_subtype: str, bill_id: str,
                     model_name: str, prompt_tokens: int, completion_tokens: int,
                     total_tokens: int, prompt_cost: float, completion_cost: float,
                     total_cost: float, response_time_ms: float, success: bool,
                     error_message: str = None, prompt_length: int = None,
                     response_length: int = None, user_id: str = None,
                     session_id: str = None) -> None:
        """Log AI usage to database for comprehensive tracking"""
        try:
            db = SessionLocal()
            try:
                usage_log = AIUsageLog(
                    id=str(uuid.uuid4()),
                    operation_type=operation_type,
                    operation_subtype=operation_subtype,
                    bill_id=bill_id,
                    model_name=model_name,
                    provider='openai',
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens,
                    prompt_cost=prompt_cost,
                    completion_cost=completion_cost,
                    total_cost=total_cost,
                    response_time_ms=response_time_ms,
                    success=success,
                    error_message=error_message,
                    prompt_length=prompt_length,
                    response_length=response_length,
                    user_id=user_id,
                    session_id=session_id
                )

                db.add(usage_log)
                db.commit()
                logger.debug(f"✅ AI usage logged: {operation_type} - ${total_cost:.4f}")

            except Exception as e:
                logger.error(f"Failed to log AI usage to database: {e}")
                db.rollback()
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Failed to create database session for AI logging: {e}")

# Global guard instance
ai_guard = AIGuard()

def get_ai_guard() -> AIGuard:
    """Get the global AI guard instance"""
    return ai_guard
