"""
AI Usage Tracking API Endpoints

Provides endpoints for monitoring AI token usage, costs, and performance metrics.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from datetime import datetime, date

from app.db.database import get_db
from app.services.ai_usage_tracking_service import AIUsageTrackingService
from app.models.ai_usage import AIUsageLog, AIUsageSummary, AIBudgetAlert

router = APIRouter()


@router.get("/bills/{bill_id}/ai-costs")
async def get_bill_ai_costs(
    bill_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get AI costs and usage for a specific bill
    
    Returns detailed breakdown of AI operations, token usage, and costs for the bill.
    """
    try:
        tracking_service = AIUsageTrackingService(db)
        costs = tracking_service.get_bill_ai_costs(bill_id)
        
        if 'error' in costs:
            raise HTTPException(status_code=500, detail=costs['error'])
        
        return {
            "success": True,
            "data": costs
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get bill AI costs: {str(e)}")


@router.get("/usage/daily-summary")
async def get_daily_usage_summary(
    days: int = Query(30, ge=1, le=365, description="Number of days to include in summary"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get daily AI usage summary for the last N days
    
    Returns aggregated usage statistics, costs, and performance metrics.
    """
    try:
        tracking_service = AIUsageTrackingService(db)
        summary = tracking_service.get_daily_usage_summary(days)
        
        if 'error' in summary:
            raise HTTPException(status_code=500, detail=summary['error'])
        
        return {
            "success": True,
            "data": summary
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get daily usage summary: {str(e)}")


@router.get("/usage/logs")
async def get_usage_logs(
    limit: int = Query(100, ge=1, le=1000, description="Number of logs to return"),
    operation_type: Optional[str] = Query(None, description="Filter by operation type"),
    model_name: Optional[str] = Query(None, description="Filter by model name"),
    bill_id: Optional[str] = Query(None, description="Filter by bill ID"),
    success_only: Optional[bool] = Query(None, description="Filter by success status"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get recent AI usage logs with optional filtering
    
    Returns detailed logs of individual AI operations for debugging and analysis.
    """
    try:
        query = db.query(AIUsageLog).order_by(AIUsageLog.created_at.desc())
        
        # Apply filters
        if operation_type:
            query = query.filter(AIUsageLog.operation_type == operation_type)
        if model_name:
            query = query.filter(AIUsageLog.model_name == model_name)
        if bill_id:
            query = query.filter(AIUsageLog.bill_id == bill_id)
        if success_only is not None:
            query = query.filter(AIUsageLog.success == success_only)
        
        logs = query.limit(limit).all()
        
        return {
            "success": True,
            "data": {
                "logs": [
                    {
                        "id": log.id,
                        "operation_type": log.operation_type,
                        "operation_subtype": log.operation_subtype,
                        "model_name": log.model_name,
                        "bill_id": log.bill_id,
                        "total_tokens": log.total_tokens,
                        "total_cost": round(log.total_cost, 4),
                        "response_time_ms": log.response_time_ms,
                        "success": log.success,
                        "error_message": log.error_message,
                        "created_at": log.created_at.isoformat()
                    } for log in logs
                ],
                "total_returned": len(logs),
                "filters_applied": {
                    "operation_type": operation_type,
                    "model_name": model_name,
                    "bill_id": bill_id,
                    "success_only": success_only
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get usage logs: {str(e)}")


@router.get("/usage/stats")
async def get_usage_stats(
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get overall AI usage statistics
    
    Returns high-level statistics about AI usage across the platform.
    """
    try:
        from sqlalchemy import func
        
        # Get overall stats
        total_logs = db.query(func.count(AIUsageLog.id)).scalar() or 0
        total_cost = db.query(func.sum(AIUsageLog.total_cost)).scalar() or 0.0
        total_tokens = db.query(func.sum(AIUsageLog.total_tokens)).scalar() or 0
        avg_cost_per_request = db.query(func.avg(AIUsageLog.total_cost)).scalar() or 0.0
        avg_tokens_per_request = db.query(func.avg(AIUsageLog.total_tokens)).scalar() or 0.0
        
        # Get stats by operation type
        operation_stats = db.query(
            AIUsageLog.operation_type,
            func.count(AIUsageLog.id).label('requests'),
            func.sum(AIUsageLog.total_cost).label('total_cost'),
            func.sum(AIUsageLog.total_tokens).label('total_tokens'),
            func.avg(AIUsageLog.response_time_ms).label('avg_response_time')
        ).group_by(AIUsageLog.operation_type).all()
        
        # Get stats by model
        model_stats = db.query(
            AIUsageLog.model_name,
            func.count(AIUsageLog.id).label('requests'),
            func.sum(AIUsageLog.total_cost).label('total_cost'),
            func.sum(AIUsageLog.total_tokens).label('total_tokens')
        ).group_by(AIUsageLog.model_name).all()
        
        return {
            "success": True,
            "data": {
                "overall": {
                    "total_requests": total_logs,
                    "total_cost": round(total_cost, 4),
                    "total_tokens": total_tokens,
                    "avg_cost_per_request": round(avg_cost_per_request, 4),
                    "avg_tokens_per_request": round(avg_tokens_per_request, 2)
                },
                "by_operation": [
                    {
                        "operation_type": stat.operation_type,
                        "requests": stat.requests,
                        "total_cost": round(float(stat.total_cost or 0), 4),
                        "total_tokens": stat.total_tokens or 0,
                        "avg_response_time_ms": round(float(stat.avg_response_time or 0), 2)
                    } for stat in operation_stats
                ],
                "by_model": [
                    {
                        "model_name": stat.model_name,
                        "requests": stat.requests,
                        "total_cost": round(float(stat.total_cost or 0), 4),
                        "total_tokens": stat.total_tokens or 0
                    } for stat in model_stats
                ]
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get usage stats: {str(e)}")


@router.get("/budget/alerts")
async def get_budget_alerts(
    active_only: bool = Query(True, description="Return only active alerts"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get budget alerts configuration and status
    """
    try:
        query = db.query(AIBudgetAlert)
        if active_only:
            query = query.filter(AIBudgetAlert.is_active == True)
        
        alerts = query.all()
        
        return {
            "success": True,
            "data": {
                "alerts": [
                    {
                        "id": alert.id,
                        "alert_name": alert.alert_name,
                        "alert_type": alert.alert_type,
                        "threshold_amount": alert.threshold_amount,
                        "threshold_tokens": alert.threshold_tokens,
                        "operation_type": alert.operation_type,
                        "model_name": alert.model_name,
                        "is_active": alert.is_active,
                        "last_triggered": alert.last_triggered.isoformat() if alert.last_triggered else None,
                        "times_triggered": alert.times_triggered
                    } for alert in alerts
                ]
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get budget alerts: {str(e)}")


@router.post("/budget/alerts")
async def create_budget_alert(
    alert_data: Dict[str, Any],
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create a new budget alert
    
    Example request body:
    {
        "alert_name": "Daily Spending Limit",
        "alert_type": "daily",
        "threshold_amount": 50.0,
        "operation_type": null,
        "model_name": null,
        "notification_emails": ["<EMAIL>"]
    }
    """
    try:
        import uuid
        
        alert = AIBudgetAlert(
            id=str(uuid.uuid4()),
            alert_name=alert_data.get('alert_name'),
            alert_type=alert_data.get('alert_type'),
            threshold_amount=alert_data.get('threshold_amount'),
            threshold_tokens=alert_data.get('threshold_tokens'),
            operation_type=alert_data.get('operation_type'),
            model_name=alert_data.get('model_name'),
            notification_emails=alert_data.get('notification_emails', [])
        )
        
        db.add(alert)
        db.commit()
        
        return {
            "success": True,
            "data": {
                "alert_id": alert.id,
                "message": "Budget alert created successfully"
            }
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create budget alert: {str(e)}")
